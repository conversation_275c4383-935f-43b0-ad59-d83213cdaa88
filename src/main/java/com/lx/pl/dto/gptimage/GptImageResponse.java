package com.lx.pl.dto.gptimage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * GPT Image API响应类
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GptImageResponse {

    /**
     * 任务ID（内部生成，用于异步处理）
     */
    private String taskId;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 创建时间戳
     */
    private Long created;

    /**
     * 生成的图像数据列表
     */
    private List<ImageData> data;

    /**
     * 错误信息
     */
    private ErrorInfo error;

    /**
     * 图像数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ImageData {
        /**
         * 图像URL
         */
        private String url;

        /**
         * Base64编码的图像数据
         */
        @JsonProperty("b64_json")
        private String b64Json;

        /**
         * 修订后的提示词
         */
        @JsonProperty("revised_prompt")
        private String revisedPrompt;
    }

    /**
     * 错误信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ErrorInfo {
        /**
         * 错误类型
         */
        private String type;

        /**
         * 错误消息
         */
        private String message;

        /**
         * 错误参数
         */
        private String param;

        /**
         * 错误代码
         */
        private String code;
    }

    /**
     * 创建任务响应
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CreateTaskResponse {
        /**
         * 任务ID
         */
        private String taskId;

        /**
         * 任务状态
         */
        private String status;

        /**
         * 创建时间
         */
        private Long created;
    }

    /**
     * 任务状态响应
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskStatusResponse {
        /**
         * 任务ID
         */
        private String taskId;

        /**
         * 任务状态
         */
        private String status;

        /**
         * 进度（0-100）
         */
        private Integer progress;

        /**
         * 结果数据
         */
        private List<ImageData> data;

        /**
         * 错误信息
         */
        private ErrorInfo error;
    }
}
