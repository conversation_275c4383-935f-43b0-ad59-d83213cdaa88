package com.lx.pl.openai.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.client.GptImageApiClient;
import com.lx.pl.config.GptImageConfig;
import com.lx.pl.constant.LockPrefixConstant;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.Resolution;
import com.lx.pl.dto.mq.MjImageProcessVo;
import com.lx.pl.enums.FeaturesType;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.enums.TaskTypeForMq;
import com.lx.pl.enums.VipType;
import com.lx.pl.exception.LogicException;
import com.lx.pl.mq.message.CommonMqMessage;
import com.lx.pl.mq.message.RMqMessage;
import com.lx.pl.mq.producer.NormalMessageProducer;
import com.lx.pl.openai.dto.images.OpenAiImagesRequest;
import com.lx.pl.openai.dto.images.OpenAiImagesResponse;
import com.lx.pl.service.GenService;
import com.lx.pl.service.LumenService;
import com.lx.pl.service.PromptFiltrationService;
import com.lx.pl.service.RedisService;
import com.lx.pl.service.VipService;
import com.lx.pl.util.AspectRatioUtils;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.lx.pl.service.LoadBalanceService.USER_TASK_TIMESTAMP;

/**
 * OpenAI Images API服务 - GPT Image 1 独立实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OpenAiImagesService {

    @Value("${bad.words.filter}")
    Boolean badWordsFilter;

    @Value("${gptImage1.modelId}")
    String gptImage1ModelId;

    @Value("${rocketmq.image.process.tag}")
    private String mjImageProcessTag;

    @Value("${rocketmq.piclumen.topic}")
    private String mjImageProcessTopic;

    // GPT Image 相关常量
    private static final String GPT_IMAGE_USER_CONCURRENT_PREFIX = "gpt_image:concurrent";
    private static final String GPT_IMAGE_TASK_LOCK = "gpt_image_task_";
    private static final String GPT_IMAGE_TASK_PREFIX = "gpt_image:task:";
    private static final String GPT_IMAGE_IMG_PREFIX = "gpt_image:do_img:";

    @Autowired
    private GptImageApiClient gptImageApiClient;

    @Autowired
    private GptImageConfig gptImageConfig;

    @Autowired
    private GenService genService;

    @Autowired
    private VipService vipService;

    @Autowired
    private PromptFiltrationService promptFiltrationService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private LumenService lumenService;

    @Autowired
    private NormalMessageProducer normalMessageProducer;

//    /**
//     * 生成图像（OpenAI标准接口）
//     */
//    public OpenAiImagesResponse generateImages(OpenAiImagesRequest request, User user, String platform) {
//        log.info("GPT Image API request from user: {}, prompt: {}, model: {}",
//                user.getLoginName(), request.getPrompt(), request.getModel());
//
//        // 验证请求参数
//        request.validate();
//
//        // Prompt过滤验证
//        validatePrompt(request.getPrompt(), platform);
//
//        // 权限和点数验证
//        validateUserPermissions(user, request);
//
//        // 检查并发任务数限制
//        validateConcurrentJobs(user);
//
//        // 生成taskId
//        String taskId = LogicConstants.GPTIMAGE1_MARKID_PREFIX + UUID.randomUUID();
//
//        try {
//            // 模拟异步处理：立即返回任务ID，然后异步调用真实API
//            CompletableFuture.runAsync(() -> {
//                processImageGenerationAsync(taskId, request, user, platform);
//            });
//
//            // 构建异步响应
//            return buildAsyncResponse(taskId, request);
//
//        } catch (Exception e) {
//            log.error("Failed to create GPT Image task for user: {}, error: {}", user.getLoginName(), e.getMessage(), e);
//            throw new LogicException(LogicErrorCode.UNKNOWN_ERROR);
//        }
//    }

    /**
     * 创建GPT Image任务（兼容原接口）
     */
    public void createGptImageTask(String prompt, User user, String markId, Boolean fastHour,
                                   String platform, GenGenericPara genParameters, String feature) {
        try {
            log.info("Creating GPT Image task for user: {}, markId: {}, prompt: {}",
                    user.getLoginName(), markId, prompt);

            // 保存PromptRecord到数据库
            savePromptRecord(markId, prompt, user, markId, fastHour, platform, genParameters, feature);

            // 保存任务状态到Redis
            saveGptImageTaskStatusToRedis(markId, markId, user.getLoginName());

            // 直接异步调用API，不需要轮询
            CompletableFuture.runAsync(() -> {
                processDirectApiCall(markId, genParameters, user.getLoginName());
            });

        } catch (Exception e) {
            log.error("Failed to create GPT Image task for user: {}, markId: {}", user.getLoginName(), markId, e);
            throw new LogicException(LogicErrorCode.UNKNOWN_ERROR);
        }
    }

//    /**
//     * 异步处理图像生成
//     */
//    private void processImageGenerationAsync(String taskId, OpenAiImagesRequest request, User user, String platform) {
//        try {
//            // 延迟一段时间模拟处理
//            Thread.sleep(gptImageConfig.getAsyncProcessDelayMs());
//
//            // 构建生成参数
//            GenGenericPara genParameters = buildGenParameters(request);
//
//            // 保存PromptRecord到数据库
//            savePromptRecord(taskId, request.getPrompt(), user, taskId, true, platform, genParameters, FeaturesType.ttp.getValue());
//
//            // 保存任务状态到Redis
//            saveGptImageTaskStatusToRedis(taskId, taskId, user.getLoginName());
//
//            // 直接调用API处理
//            processDirectApiCall(taskId, genParameters, user.getLoginName());
//
//        } catch (Exception e) {
//            log.error("Failed to process GPT Image generation async for taskId: {}", taskId, e);
//            handleTaskFailure(taskId, user.getLoginName());
//        }
//    }

    /**
     * 直接调用API处理
     */
    private void processDirectApiCall(String taskId, GenGenericPara genParameters, String loginName) {
        try {
            log.info("Processing direct API call for taskId: {}", taskId);

            // 构建OpenAI请求
            OpenAiImagesRequest apiRequest = buildOpenAiRequest(genParameters);

            // 调用真实的GPT Image API
            OpenAiImagesResponse apiResponse = callGptImageApiSync(apiRequest);

            // 处理成功结果
            handleTaskSuccess(TaskTypeForMq.GPT_IMAGE.getType(), taskId, loginName, apiResponse);

        } catch (Exception e) {
            log.error("Process direct API call error for taskId: {}", taskId, e);
            handleTaskFailure(taskId, loginName);
        }
    }

    /**
     * 构建OpenAI请求参数
     */
    private OpenAiImagesRequest buildOpenAiRequest(GenGenericPara genParameters) {
        OpenAiImagesRequest request = new OpenAiImagesRequest();
        request.setPrompt(genParameters.getPrompt());
        request.setModel("gpt-image-1");

        // 设置尺寸
        if (genParameters.getResolution() != null) {
            int width = genParameters.getResolution().getWidth();
            int height = genParameters.getResolution().getHeight();
            request.setN(genParameters.getResolution().getBatch_size());
            request.setSize(width + "x" + height);
        } else {
            request.setSize("1024x1024"); // 默认尺寸
        }
        String quality = genParameters.getGptImage1CreatePara() != null ? genParameters.getGptImage1CreatePara().getQuality() : "auto";
        // 设置其他参数
        request.setQuality(quality);

        return request;
    }

    /**
     * 同步调用GPT Image API
     */
    private OpenAiImagesResponse callGptImageApiSync(OpenAiImagesRequest request) throws IOException {
        String authorization = "Bearer " + gptImageConfig.getApiKey();
        Call<OpenAiImagesResponse> call = gptImageApiClient.generateImages(authorization, request);
        Response<OpenAiImagesResponse> response = call.execute();

        // 检查响应状态，根据不同的状态码处理错误
        if (!response.isSuccessful()) {
            String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
            log.error("GPT Image API call failed: {}", errorBody);
            throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
        }

        return response.body();
    }

    /**
     * 保存PromptRecord到数据库
     */
    private void savePromptRecord(String taskId, String prompt, User user, String markId,
                                  Boolean fastHour, String platform, GenGenericPara genParameters, String feature) {
        try {
            PromptRecord promptRecord = new PromptRecord();
            promptRecord.setLoginName(user.getLoginName());
            promptRecord.setPromptId(taskId);
            promptRecord.setPrompt(prompt);
            promptRecord.setNegativePrompt(""); // GPT Image没有负面提示词
            promptRecord.setCreateBy(user.getLoginName());
            promptRecord.setCreateTime(LocalDateTime.now());
            promptRecord.setGenStartTime(LocalDateTime.now());
            promptRecord.setBatchSize(genParameters.getResolution().getBatch_size());

            if (FeaturesType.ttp.getValue().equals(feature)) {
                promptRecord.setOriginCreate(OriginCreate.create.getValue());
            }

            // 设置宽高比，根据前端SHAPE_ALL配置处理
            String aspectRatio = "1024 x 1024"; // 默认值
            if (genParameters instanceof GenGenericPara) {
                GenGenericPara genPara = (GenGenericPara) genParameters;
                aspectRatio = AspectRatioUtils.getAspectRatio(genPara);
            }

            // 如果有原始参数，保存为genInfo
            if (genParameters != null) {
                promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genParameters));
            }
            promptRecord.setAspectRatio(aspectRatio);

            promptRecord.setModelId(gptImage1ModelId);
            promptRecord.setMarkId(markId);
            promptRecord.setFeatureName(feature);
            promptRecord.setFastHour(fastHour);
            promptRecord.setPlatform(platform);
            promptRecord.setQuality(genParameters.getGptImage1CreatePara().getQuality());
            promptRecord.setPromptParams(JsonUtils.writeToJsonNode(genParameters));

            promptRecordMapper.insert(promptRecord);

            log.debug("Saved GPT Image PromptRecord: taskId={}, markId={}, loginName={}", taskId, markId, user.getLoginName());

        } catch (Exception e) {
            log.error("Save GPT Image PromptRecord error", e);
        }
    }

    /**
     * 保存GPT Image任务状态到Redis
     */
    private void saveGptImageTaskStatusToRedis(String taskId, String markId, String loginName) {
        try {
            // 1. 保存taskId -> markId的映射
            redisService.stringSet(taskId, markId, 2, TimeUnit.HOURS);

            // 2. 保存markId -> loginName的映射
            redisService.stringSet(markId, loginName, 2, TimeUnit.HOURS);

            // 3. 在用户hash中设置任务状态为0（直接进入开始执行状态）
            redisService.putDataToHash(loginName, markId, 0, 2, TimeUnit.HOURS);

            // 4. 设置任务时间戳
            redisService.set(USER_TASK_TIMESTAMP + markId, System.currentTimeMillis(), 2, TimeUnit.HOURS);

            String taskKey = GPT_IMAGE_TASK_PREFIX + taskId;
            redisService.stringSet(taskKey, markId, 10, TimeUnit.MINUTES);

            // 添加到并发任务列表
            String userConcurrentKey = GPT_IMAGE_USER_CONCURRENT_PREFIX;
            redisService.putDataToHash(userConcurrentKey, taskId, System.currentTimeMillis());

            // 预扣
            lumenService.notFinishTask(loginName);
            log.debug("Saved GPT Image task status to Redis: taskId={}, markId={}, loginName={}", taskId, markId, loginName);

        } catch (Exception e) {
            log.error("Save GPT Image task status to Redis error", e);
        }
    }


    /**
     * 检查用户GPT Image并发任务数是否超过限制
     */
    public boolean checkGptImageConcurrentJobs(User user, String taskId) {
        String lockKey = LockPrefixConstant.CONCURRENT_EXECUTION_LOCK_PREFIX + "gpt_image";
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();

            // 获取用户当前的GPT Image任务列表
            String userConcurrentKey = GPT_IMAGE_USER_CONCURRENT_PREFIX;
            List<String> userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 清理已过期的任务
            cleanExpiredGptImageTasks(userConcurrentKey, userTaskList);

            // 重新获取清理后的任务列表
            userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 检查是否超过最大并发数限制
            int maxConcurrentJobs = gptImageConfig.getMaxConcurrentJobs() != null ?
                    gptImageConfig.getMaxConcurrentJobs() : 10;

            if (userTaskList.size() >= maxConcurrentJobs) {
                log.warn("User {} GPT Image concurrent jobs limit exceeded: {}/{}",
                        user.getLoginName(), userTaskList.size(), maxConcurrentJobs);
                return true; // 超过限制
            }

            return false; // 未超过限制

        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 清理已过期的GPT Image任务
     */
    private void cleanExpiredGptImageTasks(String userConcurrentKey, List<String> userTaskList) {
        long currentTime = System.currentTimeMillis();
        long expireTime = gptImageConfig.getTaskTimeoutSeconds() * 1000L; // 转换为毫秒

        for (String taskId : userTaskList) {
            try {
                Object timestampObj = redisService.getDataFromHash(userConcurrentKey, taskId);
                if (timestampObj != null) {
                    long timestamp = Long.parseLong(timestampObj.toString());
                    if (currentTime - timestamp > expireTime) {
                        redisService.deleteFieldFromHash(userConcurrentKey, taskId);
                        log.debug("Cleaned expired GPT Image task: {}", taskId);
                    }
                }
            } catch (Exception e) {
                log.warn("Error cleaning expired GPT Image task: {}", taskId, e);
                redisService.deleteFieldFromHash(userConcurrentKey, taskId);
            }
        }
    }

    /**
     * 处理任务成功
     */
    public void handleTaskSuccess(String taskType, String taskId, String loginName, OpenAiImagesResponse apiResponse) {
        RLock lock = redissonClient.getLock(GPT_IMAGE_TASK_LOCK + taskId);

        try {
            lock.lock();
            // 图片处理锁-防止重复处理
            String taskKey = GPT_IMAGE_IMG_PREFIX + taskId;
            String s = redisService.stringGet(taskKey);
            if (StringUtil.isNotBlank(s)) {
                return;
            }

            redisService.stringSet(taskKey, taskId, 5, TimeUnit.MINUTES);

            if (promptRecordFinished(taskId, loginName)) {
                return;
            }

            if (apiResponse == null || apiResponse.getData() == null || apiResponse.getData().isEmpty()) {
                log.warn("API response is null or empty for successful taskId: {}", taskId);
                return;
            }

            // 发送图片处理MQ消息
            OpenAiImagesResponse.ImageData imageData = apiResponse.getData().get(0);
            if (StringUtil.isNotBlank(imageData.getUrl())) {
                sendImageProcessMessage(taskType, taskId, loginName, imageData);
            }

            log.info("Successfully processed GPT Image task completion for taskId: {}", taskId);

        } finally {
            lock.unlock();
        }
    }

    /**
     * 处理任务失败
     */
    public void handleTaskFailure(String jobId, String loginName) {
        String lockKey = GPT_IMAGE_TASK_LOCK + jobId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();
            // 更新任务状态为失败
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, jobId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getDel, true)
                    .set(PromptRecord::getFailureMessage, "Task failed")
                    .set(PromptRecord::getUpdateTime, LocalDateTime.now());

            promptRecordMapper.update(null, updateWrapper);

            // 清理Redis任务信息
            cleanupTaskFromRedis(jobId, loginName);

            log.info("Handled GPT Image task failure for taskId: {}", jobId);

        } finally {
            lock.unlock();
        }
    }

    /**
     * 检查PromptRecord是否已完成
     */
    private boolean promptRecordFinished(String taskId, String loginName) {
        try {
            PromptRecord promptRecord = promptRecordMapper.selectOne(
                    new LambdaUpdateWrapper<PromptRecord>()
                            .eq(PromptRecord::getPromptId, taskId)
                            .eq(PromptRecord::getLoginName, loginName)
            );

            return promptRecord != null && promptRecord.getDel();
        } catch (Exception e) {
            log.error("Check prompt record finished error", e);
            return false;
        }
    }

    /**
     * 发送图片处理MQ消息
     */
    private void sendImageProcessMessage(String taskType, String jobId, String loginName, OpenAiImagesResponse.ImageData imageData) {
        try {
            String markId = redisService.stringGet(jobId);

            // 构建图片处理信息列表
            List<MjImageProcessVo.ImageInfo> imageInfos = new ArrayList<>();
            MjImageProcessVo.ImageInfo imageInfo = new MjImageProcessVo.ImageInfo();
            imageInfo.setOriginalUrl(imageData.getUrl());
            imageInfo.setFileName(extractFileNameFromUrl(imageData.getUrl()));
            // 默认图片尺寸
            imageInfo.setWidth(1024);
            imageInfo.setHeight(1024);
            imageInfos.add(imageInfo);

            MjImageProcessVo processVo = new MjImageProcessVo();
            processVo.setTaskType(taskType);
            processVo.setJobId(jobId);
            processVo.setLoginName(loginName);
            processVo.setMarkId(markId);
            processVo.setImageInfos(imageInfos);
            processVo.setNsfwCheck(false);

            CommonMqMessage<MjImageProcessVo> mqMessage = new RMqMessage<>(
                    mjImageProcessTopic,
                    mjImageProcessTag,
                    jobId + "_image_process"
            );
            mqMessage.setMessage(processVo);

            normalMessageProducer.syncSend(mqMessage);

            log.info("Sent GPT Image process message for jobId: {}, imageCount: {}", jobId, imageInfos.size());
        } catch (Exception e) {
            log.error("Failed to send GPT Image process message for jobId: {}", jobId, e);
        }
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String url) {
        if (StringUtil.isBlank(url)) {
            return "gpt_image_" + System.currentTimeMillis() + ".png";
        }

        try {
            String fileName = url.substring(url.lastIndexOf("/") + 1);
            if (fileName.contains("?")) {
                fileName = fileName.substring(0, fileName.indexOf("?"));
            }
            if (!fileName.contains(".")) {
                fileName += ".png";
            }
            return fileName;
        } catch (Exception e) {
            return "gpt_image_" + System.currentTimeMillis() + ".png";
        }
    }

    /**
     * 清理Redis任务信息
     */
    public void cleanupTaskFromRedis(String taskId, String loginName) {
        try {
            // 清理任务相关的Redis数据
            String markId = redisService.stringGet(taskId);

            if (StringUtil.isNotBlank(markId)) {
                redisService.deleteFieldFromHash(loginName, markId);
                // 删除markId -> loginName映射，先别删除，轮询时候再去删除
//                redisService.delete(markId);
                redisService.delete(USER_TASK_TIMESTAMP + markId);
            }

            redisService.delete(taskId);
            redisService.delete(GPT_IMAGE_TASK_PREFIX + taskId);
            // 删除图片处理锁-防止重复处理
            String imgKey = GPT_IMAGE_IMG_PREFIX + taskId;
            redisService.delete(imgKey);

            // 从并发任务列表中移除
            removeGptImageConcurrentJob(loginName, taskId);

            // 刷新预扣任务
            lumenService.notFinishTask(loginName);

            log.debug("Cleaned up GPT Image task from Redis: taskId={}, loginName={}", taskId, loginName);

        } catch (Exception e) {
            log.error("Cleanup GPT Image task from Redis error", e);
        }
    }

    /**
     * 移除用户的GPT Image并发任务
     */
    public void removeGptImageConcurrentJob(String loginName, String taskId) {
        try {
            String userConcurrentKey = GPT_IMAGE_USER_CONCURRENT_PREFIX;
            redisService.deleteFieldFromHash(userConcurrentKey, taskId);
            log.debug("Removed GPT Image concurrent job: {} for user: {}", taskId, loginName);
        } catch (Exception e) {
            log.error("Remove GPT Image concurrent job error", e);
        }
    }

}
