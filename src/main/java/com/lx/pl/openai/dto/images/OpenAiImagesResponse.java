package com.lx.pl.openai.dto.images;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * OpenAI Images API响应
 * 兼容OpenAI DALL-E API标准
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpenAiImagesResponse {

    /**
     * 创建时间戳
     */
    @Schema(description = "创建时间戳", example = "1589478378")
    private Long created;

    /**
     * 生成的图像数据列表
     */
    @Schema(description = "生成的图像数据列表")
    private List<ImageData> data;

    /**
     * 图像数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImageData {

        /**
         * 图像URL（当response_format为url时）
         */
        @Schema(description = "图像URL", example = "https://example.com/image.png")
        private String url;

        /**
         * Base64编码的图像数据（当response_format为b64_json时）
         */
        @JsonProperty("b64_json")
        @Schema(description = "Base64编码的图像数据")
        private String b64Json;

        /**
         * 修订后的提示词（DALL-E-3可能会修改原始提示词）
         */
        @JsonProperty("revised_prompt")
        @Schema(description = "修订后的提示词")
        private String revisedPrompt;
    }

    /**
     * 创建成功响应
     */
    public static OpenAiImagesResponse success(List<ImageData> imageDataList) {
        return OpenAiImagesResponse.builder()
                .created(System.currentTimeMillis() / 1000)
                .data(imageDataList)
                .build();
    }

    /**
     * 创建单个图像响应
     */
    public static OpenAiImagesResponse success(String imageUrl, String revisedPrompt) {
        ImageData imageData = ImageData.builder()
                .url(imageUrl)
                .revisedPrompt(revisedPrompt)
                .build();
        
        return success(List.of(imageData));
    }

    /**
     * 创建Base64响应
     */
    public static OpenAiImagesResponse successWithBase64(String base64Data, String revisedPrompt) {
        ImageData imageData = ImageData.builder()
                .b64Json(base64Data)
                .revisedPrompt(revisedPrompt)
                .build();
        
        return success(List.of(imageData));
    }
}
