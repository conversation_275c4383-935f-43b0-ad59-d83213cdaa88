package com.lx.pl.openai.dto.images;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * OpenAI Images API请求参数
 *
 * <AUTHOR>
 */
@Data
public class OpenAiImagesRequest {

    /**
     * 图像描述提示词
     */
    @NotBlank(message = "提示词不能为空")
    @Schema(description = "图像描述提示词，最大长度32000字符", example = "A cute baby sea otter")
    private String prompt;

    /**
     * Allows to set transparency for the background of the generated image(s). This parameter is only supported for gpt-image-1. Must be one of transparent, opaque or auto (default value). When auto is used, the model will automatically determine the best background for the image.
     * <p>
     * If transparent, the output format needs to support transparency, so it should be set to either png (default value) or webp.
     */
    @Schema(description = "背景设置", example = "transparent", allowableValues = {"transparent", "opaque", "auto"})
    private String background = "auto";

    /**
     * 使用的模型
     */
    @Schema(description = "使用的模型", example = "gpt-image-1", allowableValues = {"gpt-image-1"})
    private String model = "gpt-image-1";

    /**
     * moderation
     * string or null
     * <p>
     * Optional
     * Defaults to auto
     * Control the content-moderation level for images generated by gpt-image-1. Must be either low for less restrictive filtering or auto (default value).
     */
    @Schema(description = "内容审核级别", example = "low", allowableValues = {"low", "auto"})
    private String moderation = "low";

    /**
     * 生成图像的数量
     * integer or null
     * <p>
     * Optional
     * Defaults to 1
     * The number of images to generate. Must be between 1 and 10
     */
    @Min(value = 1, message = "图像数量至少为1")
    @Max(value = 10, message = "图像数量最多为10")
    @Schema(description = "生成图像的数量", example = "1", allowableValues = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"})
    private Integer n = 1;

    /**
     * output_compression
     * integer or null
     * <p>
     * Optional
     * Defaults to 100
     * The compression level (0-100%) for the generated images. This parameter is only supported for gpt-image-1 with the webp or jpeg output formats, and defaults to 100.
     */
    @Schema(description = "输出压缩级别", example = "100", allowableValues = {"0", "100"})
    @Min(value = 0, message = "压缩级别不能小于0")
    @Max(value = 100, message = "压缩级别不能大于100")
    private Integer output_compression;

    /**
     * output_format
     * string or null
     * <p>
     * Optional
     * Defaults to png
     * The output format for the generated images. This parameter is only supported for gpt-image-1, and defaults to png.
     */
    @Schema(description = "输出格式", example = "png", allowableValues = {"png", "webp", "jpeg"})
    private String output_format = "webp";

    /**
     * 图像质量
     * quality
     * string or null
     * Optional
     * Defaults to auto
     * high, medium and low are supported for gpt-image-1
     * The quality of the image that will be generated.
     */
    @Schema(description = "图像质量", example = "auto", allowableValues = {"auto", "high", "medium", "low"})
    private String quality = "auto";

//    /**
//     * 响应格式(This parameter isn't supported for gpt-image-1 which will always return base64-encoded images.)
//     */
//    @JsonProperty("response_format")
//    @Pattern(regexp = "^(url|b64_json)$", message = "响应格式必须是url或b64_json")
//    @Schema(description = "响应格式", example = "url", allowableValues = {"url", "b64_json"})
//    private String responseFormat = "url";

    /**
     * 图像尺寸
     * size
     * string or null
     * <p>
     * Optional
     * Defaults to auto
     * The size of the generated images. Must be one of 1024x1024, 1536x1024 (landscape), 1024x1536 (portrait), or auto (default value) for gpt-image-1.
     */
    @Schema(description = "图像尺寸", example = "1024x1024",
            allowableValues = {"1024x1024", "1536x1024", "1024x1536", "auto"})
    private String size = "1024x1024";

    /**
     * 用户标识符
     */
    @Schema(description = "用户标识符，用于监控和滥用检测", example = "user-123")
    private String user;

    /**
     * 验证请求参数
     */
    public void validate() {
        // 验证模型
        if (!"gpt-image-1".equals(model)) {
            throw new IllegalArgumentException("Only gpt-image-1 model is supported");
        }

        // gpt-image-1只支持生成1-10张图片
        if (n != null && n > 10) {
            throw new IllegalArgumentException("gpt-image-1 only supports generating less than 10 images at a time");
        }

        // 提示词长度限制
        if (prompt != null && prompt.length() > 32000) {
            throw new IllegalArgumentException("Prompt must be at most 32000 characters");
        }
    }

}
