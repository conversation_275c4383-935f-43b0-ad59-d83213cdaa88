package com.lx.pl.openai.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.generic.R;
import com.lx.pl.enums.FeaturesType;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.enums.VipType;
import com.lx.pl.exception.LogicException;
import com.lx.pl.openai.service.OpenAiImagesService;
import com.lx.pl.service.GenService;
import com.lx.pl.service.PromptFiltrationService;
import com.lx.pl.service.VipService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * OpenAI Images API控制器
 * 兼容OpenAI DALL-E API标准
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "OpenAI Images API")
@RestController
@RequestMapping("/v1/images")
public class OpenAiImagesController {

    @Value("${bad.words.filter}")
    Boolean badWordsFilter;

    @Value("${gptImage1.modelId}")
    String gptImage1ModelId;

    @Autowired
    private OpenAiImagesService openAiImagesService;

    @Autowired
    private GenService genService;

    @Autowired
    private VipService vipService;

    @Autowired
    private PromptFiltrationService promptFiltrationService;

//    /**
//     * 生成图像
//     * POST /v1/images/generations
//     */
//    @PostMapping("/generations")
//    @Operation(summary = "生成图像", description = "根据文本提示生成图像，兼容OpenAI Images API")
//    @Authorization
//    public ResponseEntity<?> generateImages(
//            @Parameter(hidden = true) @CurrentUser User user,
//            @RequestBody @Valid OpenAiImagesRequest request,
//            HttpServletRequest httpRequest) {
//
//        try {
//            log.info("GPT Image API request from user: {}, model: {}, prompt: {}",
//                    user.getLoginName(), request.getModel(), request.getPrompt());
//
//            // 获取平台信息
//            String platform = genService.getPlatform(httpRequest);
//            if (StringUtil.isBlank(platform)) {
//                return createErrorResponse(HttpStatus.BAD_REQUEST, "invalid_request",
//                        "Invalid platform information", null);
//            }
//
//            // 调用服务生成图像
//            OpenAiImagesResponse response = openAiImagesService.generateImages(request, user, platform);
//
//            return ResponseEntity.ok(response);
//
//        } catch (LogicException e) {
//            log.warn("Logic error in image generation: {}", e.getMessage());
//            return handleLogicException(e);
//        } catch (IllegalArgumentException e) {
//            log.warn("Invalid request parameters: {}", e.getMessage());
//            return createErrorResponse(HttpStatus.BAD_REQUEST, "invalid_request",
//                    e.getMessage(), null);
//        } catch (Exception e) {
//            log.error("Unexpected error in image generation", e);
//            return createErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "internal_error",
//                    "An unexpected error occurred", null);
//        }
//    }

//    /**
//     * 处理逻辑异常
//     */
//    private ResponseEntity<?> handleLogicException(LogicException e) {
//        String errorCode = e.getCode();
//
//        // 根据错误码进行映射
//        if ("4001".equals(errorCode)) { // ILLEGAL_PROMPT
//            return createErrorResponse(HttpStatus.BAD_REQUEST, "content_policy_violation",
//                    "Your request was rejected as a result of our safety system", null);
//        } else if ("4006".equals(errorCode)) { // NOT_ENOUGH_LUMENS_SUPPORT
//            return createErrorResponse(HttpStatus.PAYMENT_REQUIRED, "insufficient_quota",
//                    "You exceeded your current quota, please check your plan and billing details", null);
//        } else if ("4027".equals(errorCode) || "4002".equals(errorCode)) { // FLUX_EXCEED_CONCURRENT_JOBS, EXCEED_CONCURRENT_JOBS
//            return createErrorResponse(HttpStatus.TOO_MANY_REQUESTS, "rate_limit_exceeded",
//                    "Rate limit reached for requests", null);
//        } else if ("4028".equals(errorCode)) { // FLUX_PROMPT_REQUIRED
//            return createErrorResponse(HttpStatus.BAD_REQUEST, "invalid_request",
//                    "Prompt is required", null);
//        } else if ("4022".equals(errorCode)) { // ILLEGAL_REQUEST
//            return createErrorResponse(HttpStatus.BAD_REQUEST, "invalid_request",
//                    "Invalid request", null);
//        } else {
//            return createErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "internal_error",
//                    "An unexpected error occurred", null);
//        }
//    }

//    /**
//     * 创建错误响应（OpenAI格式）
//     */
//    private ResponseEntity<?> createErrorResponse(HttpStatus status, String type, String message, String param) {
//        Map<String, Object> error = new HashMap<>();
//        error.put("type", type);
//        error.put("message", message);
//        if (param != null) {
//            error.put("param", param);
//        }
//        error.put("code", null);
//
//        Map<String, Object> response = new HashMap<>();
//        response.put("error", error);
//
//        return ResponseEntity.status(status).body(response);
//    }

    /**
     * 兼容原GenController的create接口
     */
    @PostMapping("/generations/compatible")
    @Operation(summary = "GPT Image生成（兼容原接口）", description = "兼容原生图接口，使用GenGenericPara参数")
    @Authorization
    public R<Map<String, Object>> generateImagesCompatible(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody GenGenericPara genParameters,
            HttpServletRequest httpRequest) {

        log.info("GPT Image compatible create request from user: {}, prompt: {}",
                user.getLoginName(), genParameters.getPrompt());

        try {
            // 基础参数验证
            if (genParameters.getPrompt() == null || genParameters.getPrompt().isEmpty()) {
                throw new LogicException(LogicErrorCode.PROMPT_REQUIRED);
            }

            if (genParameters.getPrompt().length() > 3000) {
                return R.fail(400, "Image prompt exceeds 3000!");
            }
            if (null == genParameters.getResolution()) {
                return R.fail(400, "Image dimensions required !");
            }
            //width 必须为1024或者1536
            if (genParameters.getResolution().getWidth() != 1024 && genParameters.getResolution().getWidth() != 1536) {
                return R.fail(400, "Image width must be 1024 or 1536 !");
            }
            //height 必须为1024或者1536
            if (genParameters.getResolution().getHeight() != 1024 && genParameters.getResolution().getHeight() != 1536) {
                return R.fail(400, "Image height must be 1024 or 1536 !");
            }

            if (1 > genParameters.getResolution().getBatch_size()) {
                return R.fail(400, "At least one image required !");
            }

            if (!genService.checkPlatformModel(genParameters.getModel_id(), httpRequest)) {
                return R.fail(400, "Model not support !");
            }

            // 获取平台信息
            String platform = genService.getPlatform(httpRequest);
            if (StringUtil.isBlank(platform)) {
                throw new LogicException(LogicErrorCode.ILLEGAL_REQUEST);
            }

            // Prompt过滤验证
            Boolean filterChildSex = promptFiltrationService.filterChildSex(genParameters.getPrompt());
            if (filterChildSex) {
                throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
            }

            // Android平台badword过滤
            if ("android".equals(platform) && badWordsFilter) {
                Boolean filterBadWords = promptFiltrationService.filterBadWords(genParameters.getPrompt());
                if (filterBadWords) {
                    throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
                }
            }

            // 判断是否是普通用户
            Boolean notVip = VipType.basic.getValue().equals(user.getVipType());
            Boolean fastHour = true;

            // 检查权限
            if (notVip) {
                throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
            }

            // 校验点数
            if (!vipService.judgeUserFastCreate(gptImage1ModelId, user, genParameters.getResolution().getBatch_size(),
                    Boolean.FALSE, OriginCreate.create, null, null, platform)) {
                throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
            }

            // 设置传参
            String markId = LogicConstants.GPTIMAGE1_MARKID_PREFIX + UUID.randomUUID();
            if (genService.checkUserConcurrentJobs(user, markId, false)) {
                throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
            }

            // 检查GPT Image并发任务数限制
            if (openAiImagesService.checkGptImageConcurrentJobs(user, null)) {
                throw new LogicException(LogicErrorCode.THIRD_API_EXCEED_CONCURRENT_JOBS);
            }

            // 判断生图类型
            String feature = FeaturesType.ttp.getValue();

            // 调用GPT Image服务
            openAiImagesService.createGptImageTask(
                    genParameters.getPrompt(),
                    user,
                    markId,
                    fastHour,
                    platform,
                    genParameters,
                    feature
            );

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("markId", markId);
            result.put("index", 0);
            result.put("fastHour", fastHour);
            result.put("featureName", feature);

            return R.success(result);

        } catch (LogicException e) {
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error in compatible image generation", e);
            throw new LogicException(LogicErrorCode.UNKNOWN_ERROR);
        }
    }


    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "ok");
        response.put("service", "openai-images-api");
        return ResponseEntity.ok(response);
    }
}
