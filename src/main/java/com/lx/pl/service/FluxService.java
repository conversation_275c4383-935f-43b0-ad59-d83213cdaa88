package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.client.FluxApiClient;
import com.lx.pl.config.FluxConfig;
import com.lx.pl.constant.LockPrefixConstant;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.Resolution;
import com.lx.pl.dto.flux.FluxRequest;
import com.lx.pl.dto.flux.FluxResponse;
import com.lx.pl.dto.mq.MjImageProcessVo;
import com.lx.pl.dto.mq.TaskPollingVo;
import com.lx.pl.enums.*;
import com.lx.pl.exception.LogicException;
import com.lx.pl.mq.message.CommonMqMessage;
import com.lx.pl.mq.message.RMqMessage;
import com.lx.pl.mq.producer.NormalMessageProducer;
import com.lx.pl.util.AspectRatioUtils;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.lx.pl.service.LoadBalanceService.USER_TASK_TIMESTAMP;

/**
 * Flux服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FluxService {

    @Autowired
    private FluxApiClient fluxApiClient;

    @Autowired
    private FluxConfig fluxConfig;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private PromptFileMapper promptFileMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private NormalMessageProducer normalMessageProducer;

    @Autowired
    private SendPollingService unifiedTaskPollingService;

    @Autowired
    private LumenService lumenService;

    @Value("${rocketmq.image.process.tag}")
    private String mjImageProcessTag;
    @Value("${rocketmq.piclumen.topic}")
    private String mjImageProcessTopic;

    // Redis key常量
    private static final String FLUX_TASK_LOCK = "flux_task_";
    public static final String FLUX_TASK_PREFIX = "flux:task:";
    private static final String FLUX_USER_CONCURRENT_PREFIX = "flux:concurrent";
    private static final String FLUX_IMG_PREFIX = "flux:do_img:";

    /**
     * 创建Flux Kontext Pro图像生成任务
     *
     * @param prompt        提示词
     * @param user          用户
     * @param markId        标记ID
     * @param fastHour      是否快速生图
     * @param platform      平台
     * @param genParameters 原始生图参数
     * @return 任务响应
     */
    public FluxResponse.CreateTaskResponse createKontextProTask(String prompt, User user,
                                                                String markId, Boolean fastHour, String platform, GenGenericPara genParameters, String feature) {
        try {
            // 构建请求
            FluxRequest.KontextProRequest request = new FluxRequest.KontextProRequest();
            request.setPrompt(genParameters.getPrompt());
            request.setOutputFormat(fluxConfig.getDefaultOutputFormat());
            request.setSafetyTolerance(6);
            request.setPromptUpsampling(fluxConfig.getPromptUpsamplingEnabled());

            // 设置宽高比
            if (genParameters.getResolution() != null) {
                Resolution resolution = genParameters.getResolution();
                if (resolution.getWidth() > 0 && resolution.getHeight() > 0) {
                    // 计算宽高比
                    String aspectRatio = AspectRatioUtils.getAspectRatioLabel(resolution.getWidth(), resolution.getHeight());
                    if (StringUtil.isNotBlank(aspectRatio)) {
                        request.setAspectRatio(aspectRatio);
                    }
                }
            }


            // 设置种子值
            if (genParameters != null && genParameters.getSeed() != null) {
                request.setSeed(genParameters.getSeed());
            }

            log.info("Flux Kontext Pro request: {}", JsonUtils.writeToString(request));

            // 调用API
            Call<FluxResponse.CreateTaskResponse> call = fluxApiClient.createKontextProTask(fluxConfig.getApiKey(), request);
            Response<FluxResponse.CreateTaskResponse> response = call.execute();

            if (!response.isSuccessful()) {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                log.error("Flux Kontext Pro API call failed: {}", errorBody);
                throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
            }

            FluxResponse.CreateTaskResponse responseData = response.body();
            if (responseData == null || StringUtil.isBlank(responseData.getId())) {
                log.error("Flux Kontext Pro API response is null or missing task ID");
                throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
            }

            // 添加到并发任务列表
            String userConcurrentKey = FLUX_USER_CONCURRENT_PREFIX;
            redisService.putDataToHash(userConcurrentKey, responseData.getId(), System.currentTimeMillis());

            // 如果提供了markId等参数，则保存PromptRecord到数据库
            if (StringUtil.isNotBlank(markId)) {
                savePromptRecord(responseData.getId(), prompt, user, markId, fastHour, platform, genParameters, feature);

                // 保存Flux任务状态到Redis
                saveFluxTaskStatusToRedis(responseData.getId(), markId, user.getLoginName());

                // 启动任务状态轮询，传递pollingUrl
                startTaskStatusPolling(responseData.getId(), user.getLoginName(), fluxConfig.getFirstDelaySeconds(), responseData.getPollingUrl());
            }

            log.info("Flux Kontext Pro task created successfully, taskId: {}", responseData.getId());
            return responseData;

        } catch (IOException e) {
            log.error("Flux Kontext Pro API call failed", e);
            throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
        } catch (Exception e) {
            log.error("Create Flux Kontext Pro task error", e);
            throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
        }
    }

    /**
     * 创建Flux Kontext Pro图像生成任务
     *
     * @param prompt        提示词
     * @param user          用户
     * @param markId        标记ID
     * @param fastHour      是否快速生图
     * @param platform      平台
     * @param genParameters 原始生图参数
     * @return 任务响应
     */
    public FluxResponse.CreateTaskResponse editKontextProTask(String prompt, User user,
                                                              String markId, Boolean fastHour, String platform, GenGenericPara genParameters, String feature) {
        try {
            // 构建请求
            FluxRequest.KontextProRequest request = new FluxRequest.KontextProRequest();
            request.setPrompt(genParameters.getPrompt());
            request.setOutputFormat(fluxConfig.getDefaultOutputFormat());
            request.setSafetyTolerance(2);
            //设置图片信息
            request.setInputImage(genParameters.getImgEditPara().getImgUrl());

            request.setPromptUpsampling(fluxConfig.getPromptUpsamplingEnabled());

            // 设置种子值
            if (genParameters != null && genParameters.getSeed() != null) {
                request.setSeed(genParameters.getSeed());
            }

            log.info("Flux Kontext Pro request: {}", JsonUtils.writeToString(request));

            // 调用API
            Call<FluxResponse.CreateTaskResponse> call = fluxApiClient.createKontextProTask(fluxConfig.getApiKey(), request);
            Response<FluxResponse.CreateTaskResponse> response = call.execute();

            if (!response.isSuccessful()) {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                log.error("Flux Kontext Pro API call failed: {}", errorBody);
                throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
            }

            FluxResponse.CreateTaskResponse responseData = response.body();
            if (responseData == null || StringUtil.isBlank(responseData.getId())) {
                log.error("Flux Kontext Pro API response is null or missing task ID");
                throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
            }

            // 添加到并发任务列表
            String userConcurrentKey = FLUX_USER_CONCURRENT_PREFIX;
            redisService.putDataToHash(userConcurrentKey, responseData.getId(), System.currentTimeMillis());

            // 如果提供了markId等参数，则保存PromptRecord到数据库
            if (StringUtil.isNotBlank(markId)) {
                savePromptRecord(responseData.getId(), prompt, user, markId, fastHour, platform, genParameters, feature);

                // 保存Flux任务状态到Redis
                saveFluxTaskStatusToRedis(responseData.getId(), markId, user.getLoginName());

                // 启动任务状态轮询，传递pollingUrl
                startTaskStatusPolling(responseData.getId(), user.getLoginName(), fluxConfig.getFirstDelaySeconds(), responseData.getPollingUrl());
            }

            log.info("Flux Kontext Pro task created successfully, taskId: {}", responseData.getId());
            return responseData;

        } catch (IOException e) {
            log.error("Flux Kontext Pro API call failed", e);
            throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
        } catch (Exception e) {
            log.error("Create Flux Kontext Pro task error", e);
            throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
        }
    }

    /**
     * 获取任务结果
     */
    public FluxResponse.TaskStatusResponse getTaskResult(String taskId) {
        try {
            Call<FluxResponse.TaskStatusResponse> call = fluxApiClient.getTaskResult(taskId);
            Response<FluxResponse.TaskStatusResponse> response = call.execute();

            if (!response.isSuccessful()) {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                log.error("Flux get task result API call failed: {}", errorBody);
                throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
            }

            return response.body();

        } catch (IOException e) {
            log.error("Flux get task result API call failed", e);
            throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
        }
    }

    /**
     * 通过pollingUrl获取任务结果
     */
    public FluxResponse.TaskStatusResponse getTaskResultByPollingUrl(String pollingUrl) {
        try {
            Call<FluxResponse.TaskStatusResponse> call = fluxApiClient.getTaskResultByPollingUrl(pollingUrl);
            Response<FluxResponse.TaskStatusResponse> response = call.execute();

            if (!response.isSuccessful()) {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                log.error("Flux get task result by polling URL API call failed: {}", errorBody);
                throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
            }

            return response.body();

        } catch (IOException e) {
            log.error("Flux get task result by polling URL API call failed", e);
            throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
        }
    }

    /**
     * 保存PromptRecord到数据库
     */
    private void savePromptRecord(String taskId, String prompt, User user, String markId,
                                  Boolean fastHour, String platform, GenGenericPara genParameters, String feature) {
        try {
            PromptRecord promptRecord = new PromptRecord();
            promptRecord.setLoginName(user.getLoginName());
            promptRecord.setPromptId(taskId);
            promptRecord.setPrompt(prompt);
            promptRecord.setNegativePrompt(""); // Flux没有负面提示词
            promptRecord.setCreateBy(user.getLoginName());
            promptRecord.setCreateTime(LocalDateTime.now());
            promptRecord.setGenStartTime(LocalDateTime.now());
            if (FeaturesType.ttp.getValue().equals(feature)) {
                promptRecord.setOriginCreate(OriginCreate.create.getValue());
            } else if (FeaturesType.edit.getValue().equals(feature)) {
                promptRecord.setOriginCreate(OriginCreate.edit.getValue());
            }
            promptRecord.setBatchSize(1); // Flux每次生成1张图
            promptRecord.setModelId(genParameters.getModel_id());
            promptRecord.setMarkId(markId);

            // 如果有原始参数，保存为genInfo
            if (genParameters != null) {
                promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genParameters));
            }

            // 设置宽高比，根据前端SHAPE_ALL配置处理
            String aspectRatio = "1024 x 1024"; // 默认值
            if (genParameters instanceof GenGenericPara) {
                GenGenericPara genPara = (GenGenericPara) genParameters;
                aspectRatio = AspectRatioUtils.getAspectRatio(genPara);
            }
            promptRecord.setAspectRatio(aspectRatio);
            promptRecord.setFeatureName(feature);
            promptRecord.setFastHour(fastHour != null ? fastHour : true);
            promptRecord.setPlatform(platform);

            promptRecordMapper.insert(promptRecord);
            log.info("Saved PromptRecord for Flux task: {}", taskId);

        } catch (Exception e) {
            log.error("Save PromptRecord error for task: " + taskId, e);
        }
    }

    /**
     * 保存Flux任务状态到Redis
     */
    private void saveFluxTaskStatusToRedis(String taskId, String markId, String loginName) {
        try {
            // 1. 保存taskId -> markId的映射
            redisService.stringSet(taskId, markId, 2, TimeUnit.HOURS);

            // 2. 保存markId -> loginName的映射
            redisService.stringSet(markId, loginName, 2, TimeUnit.HOURS);

            // 3. 在用户hash中设置任务状态为0（直接进入开始执行状态）
            redisService.putDataToHash(loginName, markId, 0, 2, TimeUnit.HOURS);

            // 4. 设置任务时间戳
            redisService.set(USER_TASK_TIMESTAMP + markId, System.currentTimeMillis(), 2, TimeUnit.HOURS);

            String taskKey = FLUX_TASK_PREFIX + taskId;
            redisService.stringSet(taskKey, markId, 10, TimeUnit.MINUTES);

            // 预扣
            lumenService.notFinishTask(loginName);
            log.debug("Saved Flux task status to Redis: taskId={}, markId={}, loginName={}", taskId, markId, loginName);

        } catch (Exception e) {
            log.error("Save Flux task status to Redis error", e);
        }
    }

    /**
     * 启动任务状态轮询（使用统一轮询服务）
     */
    private void startTaskStatusPolling(String taskId, String loginName, Integer delaySeconds, String pollingUrl) {
        log.info("Starting Flux task status polling for taskId: {}, user: {}", taskId, loginName);

        int maxAttempts = fluxConfig.getMaxPollingAttempts() != null ?
                fluxConfig.getMaxPollingAttempts() : 10;
        int pollingInterval = fluxConfig.getPollingIntervalSeconds() != null ?
                fluxConfig.getPollingIntervalSeconds() * 1000 : 2000; // 转换为毫秒

        // 创建轮询消息
        TaskPollingVo pollingVo = new TaskPollingVo(
                TaskTypeForMq.FLUX.getType(),
                taskId,
                loginName,
                0, // 初始轮询次数
                maxAttempts,
                pollingInterval,
                System.currentTimeMillis(),
                pollingUrl
        );

        // 发送延时消息，延时pollingInterval毫秒后开始第一次轮询
        unifiedTaskPollingService.sendPollingMessage(pollingVo, delaySeconds);
    }

    /**
     * 检查用户Flux并发任务数是否超过限制
     */
    public boolean checkFluxConcurrentJobs(User user, String taskId) {
        String lockKey = LockPrefixConstant.CONCURRENT_EXECUTION_LOCK_PREFIX + "flux";
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();

            // 获取用户当前的Flux任务列表
            String userConcurrentKey = FLUX_USER_CONCURRENT_PREFIX;
            List<String> userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 清理已过期的任务
            cleanExpiredFluxTasks(userConcurrentKey, userTaskList);

            // 重新获取清理后的任务列表
            userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 检查是否超过最大并发数限制
            int maxConcurrentJobs = fluxConfig.getMaxConcurrentJobs() != null ?
                    fluxConfig.getMaxConcurrentJobs() : 9999;

            if (userTaskList.size() >= maxConcurrentJobs) {
                log.warn("User {} Flux concurrent jobs limit exceeded: {}/{}",
                        user.getLoginName(), userTaskList.size(), maxConcurrentJobs);
                return true; // 超过限制
            }

            return false; // 未超过限制

        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 清理已过期的Flux任务
     */
    private void cleanExpiredFluxTasks(String userConcurrentKey, List<String> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        long expireTime = 2 * 60 * 60 * 1000; // 2小时过期

        for (String taskId : taskList) {
            try {
                Object timestamp = redisService.getDataFromHash(userConcurrentKey, taskId);
                if (timestamp != null) {
                    long taskTime = Long.parseLong(timestamp.toString());
                    if (currentTime - taskTime > expireTime) {
                        redisService.deleteFieldFromHash(userConcurrentKey, taskId);
                        log.debug("Cleaned expired Flux task: {}", taskId);
                    }
                }
            } catch (Exception e) {
                log.warn("Clean expired Flux task error: {}", taskId, e);
                redisService.deleteFieldFromHash(userConcurrentKey, taskId);
            }
        }
    }

    /**
     * 移除用户的Flux并发任务
     */
    public void removeFluxConcurrentJob(String loginName, String taskId) {
        try {
            String userConcurrentKey = FLUX_USER_CONCURRENT_PREFIX;
            redisService.deleteFieldFromHash(userConcurrentKey, taskId);
            log.debug("Removed Flux concurrent job: {} for user: {}", taskId, loginName);
        } catch (Exception e) {
            log.error("Remove Flux concurrent job error", e);
        }
    }

    /**
     * 处理任务成功
     */
    public void handleTaskSuccess(String taskType, String taskId, String loginName, FluxResponse.TaskStatusResponse taskStatus) {
        RLock lock = redissonClient.getLock(FLUX_TASK_LOCK + taskId);

        try {
            lock.lock();
            // 图片处理锁-防止重复处理
            String taskKey = FLUX_IMG_PREFIX + taskId;
            String s = redisService.stringGet(taskKey);
            if (StringUtil.isNotBlank(s)) {
                return;
            }

            redisService.stringSet(taskKey, taskId, 5, TimeUnit.MINUTES);

            if (promptRecordFinished(taskId, loginName)) {
                return;
            }

            FluxResponse.TaskResult taskResult = taskStatus.getResult();
            if (taskResult == null) {
                log.warn("Task result is null for successful taskId: {}", taskId);
                return;
            }

            // 发送图片处理MQ消息（使用统一的图片处理服务）
            if (StringUtil.isNotBlank(taskResult.getSample())) {
                sendImageProcessMessage(taskType, taskId, loginName, taskResult);
            }

            log.info("Successfully processed Flux task completion for taskId: {}", taskId);

        } finally {
            lock.unlock();
        }
    }

    /**
     * 处理任务失败
     */
    public void handleTaskFailure(String jobId, String loginName) {
        //分布式锁
        String lockKey = FLUX_TASK_LOCK + jobId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();
            // 更新任务状态为失败
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, jobId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getDel, true)
                    .set(PromptRecord::getFailureMessage, "Task failed")
                    .set(PromptRecord::getUpdateTime, LocalDateTime.now());

            promptRecordMapper.update(null, updateWrapper);

            // 清理Redis任务信息
            cleanupTaskFromRedis(jobId, loginName);

            log.info("Handled Flux task failure for taskId: {}", jobId);

        } catch (Exception e) {
            log.error("Error handling flux task failure for jobId: " + jobId, e);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 处理任务失败
     */
    public void handleTaskRequestModerated(String jobId, String loginName, FluxResponse.TaskStatusResponse taskStatusResponse) {
        //分布式锁
        String lockKey = FLUX_TASK_LOCK + jobId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();
            // 更新任务状态为失败
            String failureMessage = taskStatusResponse.getStatus();
            if (taskStatusResponse.getDetails() != null) {
                String errorCode = fluxErrorCode(taskStatusResponse.getDetails());
                if (errorCode != null) {
                    failureMessage = errorCode + failureMessage;
                }
                failureMessage += "&&details:" + JsonUtils.writeToString(taskStatusResponse.getDetails());
            }
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, jobId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getDel, true)
                    .set(PromptRecord::getFailureMessage, failureMessage)
                    .set(PromptRecord::getUpdateTime, LocalDateTime.now());

            promptRecordMapper.update(null, updateWrapper);

            // 清理Redis任务信息
            cleanupTaskFromRedis(jobId, loginName);

            log.info("Handled Flux task failure by Request Moderated for taskId: {}", jobId);

        } catch (Exception e) {
            log.error("Error handling flux task failure by Request Moderated for jobId: " + jobId, e);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    private String fluxErrorCode(Map<String, Object> details) {
        for (Object object : details.values()) {
            if (object == null) {
                continue;
            }
            if (object instanceof String[]) {
                List<String> list = Arrays.asList((String[]) object);
                if (list.contains(FluxModeratedReasonEnum.SAFETY_FILTER.getValue())) {
                    return ComfyStatusCodeEnum.PORNOGRAPHIC_CONTENT_DETECTED.getCode() + LogicConstants.FAIL_MSG_SPLIT;
                }
                if (list.contains(FluxModeratedReasonEnum.DERIVATIVE_WORKS_FILTER.getValue())) {
                    return ComfyStatusCodeEnum.INFRINGEMENT_RISK_DETECTED.getCode() + LogicConstants.FAIL_MSG_SPLIT;
                }
            } else {
                String string = String.valueOf(object);
                if (string.contains(FluxModeratedReasonEnum.SAFETY_FILTER.getValue())) {
                    return ComfyStatusCodeEnum.PORNOGRAPHIC_CONTENT_DETECTED.getCode() + LogicConstants.FAIL_MSG_SPLIT;
                }
                if (string.contains(FluxModeratedReasonEnum.DERIVATIVE_WORKS_FILTER.getValue())) {
                    return ComfyStatusCodeEnum.INFRINGEMENT_RISK_DETECTED.getCode() + LogicConstants.FAIL_MSG_SPLIT;
                }
            }
        }
        return null;
    }

    /**
     * 处理任务失败
     */
    public void handleTaskUnknownError(String jobId, String loginName) {
        //分布式锁
        String lockKey = FLUX_TASK_LOCK + jobId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();
            // 更新任务状态为失败
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, jobId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getDel, true)
                    .set(PromptRecord::getFailureMessage, "unknown error")
                    .set(PromptRecord::getUpdateTime, LocalDateTime.now());

            promptRecordMapper.update(null, updateWrapper);

            // 清理Redis任务信息
            cleanupTaskFromRedis(jobId, loginName);

            log.info("Handled Flux task failure by unknown error for taskId: {}", jobId);

        } catch (Exception e) {
            log.error("Error handling flux task failure by unknown error for jobId: " + jobId, e);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 处理任务超时
     */
    public void handleTaskTimeout(String jobId, String loginName) {
        try {
            // 将超时任务标记为失败
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, jobId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getDel, true)
                    .set(PromptRecord::getFailureMessage, "Task timeout")
                    .set(PromptRecord::getUpdateTime, LocalDateTime.now());

            promptRecordMapper.update(null, updateWrapper);

            // 清理Redis任务信息
            cleanupTaskFromRedis(jobId, loginName);

            log.warn("Processed timeout task for jobId: {}", jobId);

        } catch (Exception e) {
            log.error("Error handling task timeout for jobId: " + jobId, e);
        }
    }

    /**
     * 发送图片处理MQ消息
     */
    private void sendImageProcessMessage(String taskType, String jobId, String loginName, FluxResponse.TaskResult taskResult) {
        try {
            String markId = redisService.stringGet(jobId);

            // 构建图片处理信息列表
            List<MjImageProcessVo.ImageInfo> imageInfos = new ArrayList<>();
            // Flux任务只返回一个图片
            String imageUrl = taskResult.getSample();
            MjImageProcessVo.ImageInfo imageInfo = new MjImageProcessVo.ImageInfo();
            imageInfo.setOriginalUrl(imageUrl);
            imageInfo.setFileName(extractFileNameFromUrl(imageUrl));
            // 默认图片尺寸
            imageInfo.setWidth(1024);
            imageInfo.setHeight(1024);
            // 注意：这里不设置promptFileId，因为PromptFile还没有插入数据库
            // 图片处理服务需要根据jobId和imageUrl来查找对应的PromptFile记录
            imageInfos.add(imageInfo);

            MjImageProcessVo processVo = new MjImageProcessVo();
            processVo.setTaskType(taskType);
            processVo.setJobId(jobId);
            processVo.setLoginName(loginName);
            processVo.setMarkId(markId);
            processVo.setImageInfos(imageInfos);
            processVo.setNsfwCheck(true);

            CommonMqMessage<MjImageProcessVo> mqMessage = new RMqMessage<>(
                    mjImageProcessTopic,
                    mjImageProcessTag,
                    jobId + "_image_process"
            );
            mqMessage.setMessage(processVo);

            normalMessageProducer.syncSend(mqMessage);

            log.info("Sent image process message for jobId: {}, imageCount: {}", jobId, imageInfos.size());
        } catch (Exception e) {
            log.error("Failed to send image process message for jobId: {}", jobId, e);
        }
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String imageUrl) {
        if (StringUtil.isBlank(imageUrl)) {
            return "flux_image.png";
        }

        try {
            String[] parts = imageUrl.split("/");
            String lastPart = parts[parts.length - 1];

            // 移除查询参数
            if (lastPart.contains("?")) {
                lastPart = lastPart.substring(0, lastPart.indexOf("?"));
            }

            return StringUtil.isNotBlank(lastPart) ? lastPart : "flux_image.png";
        } catch (Exception e) {
            log.warn("Failed to extract filename from URL: {}", imageUrl);
            return "flux_image.png";
        }
    }

    /**
     * 轮询消息数据类
     */
    public static class FluxPollingMessage {
        private String taskId;
        private String loginName;
        private Integer attemptCount;

        public FluxPollingMessage() {
        }

        public FluxPollingMessage(String taskId, String loginName, Integer attemptCount) {
            this.taskId = taskId;
            this.loginName = loginName;
            this.attemptCount = attemptCount;
        }

        // getters and setters
        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public String getLoginName() {
            return loginName;
        }

        public void setLoginName(String loginName) {
            this.loginName = loginName;
        }

        public Integer getAttemptCount() {
            return attemptCount;
        }

        public void setAttemptCount(Integer attemptCount) {
            this.attemptCount = attemptCount;
        }
    }

    /**
     * 检查PromptRecord是否已完成
     */
    private boolean promptRecordFinished(String taskId, String loginName) {
        try {
            PromptRecord promptRecord = promptRecordMapper.selectOne(
                    new LambdaUpdateWrapper<PromptRecord>()
                            .eq(PromptRecord::getPromptId, taskId)
                            .eq(PromptRecord::getLoginName, loginName)
            );

            return promptRecord != null && promptRecord.getGenEndTime() != null;

        } catch (Exception e) {
            log.error("Check PromptRecord finished error", e);
            return false;
        }
    }

    /**
     * 清理Redis任务信息
     */
    public void cleanupTaskFromRedis(String jobId, String loginName) {
        try {
            // 获取markId
            String markId = redisService.stringGet(jobId);

            // 清理MJ任务状态（模拟传统任务的清理方式）
            if (StringUtil.isNotBlank(markId)) {
                // 从用户hash中删除任务状态(重要，删除之后，前端轮询会直接认定为任务完成进行最终处理然后删除redisService.delete(originPromptRecord.getMarkId())，不需要-2状态)
                redisService.deleteFieldFromHash(loginName, markId);

                // 删除markId -> loginName映射，先别删除，轮询时候再去删除
//                redisService.delete(markId);

                // 删除任务时间戳
                redisService.delete(USER_TASK_TIMESTAMP + markId);

                log.debug("Cleaned up Flux task status for markId: {}", markId);
            }

            // 删除jobId -> markId映射
            redisService.delete(jobId);

            // 删除任务信息
            String taskKey = FLUX_TASK_PREFIX + jobId;
            redisService.delete(taskKey);

            // 删除图片处理锁-防止重复处理
            String imgKey = FLUX_IMG_PREFIX + jobId;
            redisService.delete(imgKey);

            // 从并发任务列表中删除
            removeFluxConcurrentJob(loginName, jobId);

            // 刷新预扣任务
            lumenService.notFinishTask(loginName);
            log.info("Cleaned up all Redis data for jobId: {}, markId: {}", jobId, markId);

        } catch (Exception e) {
            log.error("Error cleaning up task from redis for jobId: " + jobId, e);
        }
    }

}
