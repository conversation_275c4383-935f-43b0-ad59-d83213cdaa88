package com.lx.pl.service;

import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.dto.mq.MjImageProcessResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;

import static com.lx.pl.service.GenService.USER_TODAY_CREATE_IMG_NUMS;
import static com.lx.pl.service.ImgUploadCommonService.getFilePath;

/**
 * MJ图片处理结果服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MjImageProcessResultService {

    @Autowired
    private PromptFileMapper promptFileMapper;

    @Autowired
    private VipService vipService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private MidjourneyService midjourneyService;

    /**
     * 处理图片处理结果（直接入库，参考原来processTaskImages的模式）
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleImageProcessResult(MjImageProcessResultVo resultVo) {
        try {
            log.info("开始处理MJ图片处理结果，jobId: {}, status: {}", resultVo.getJobId(), resultVo.getStatus());

            if ("SUCCESS".equals(resultVo.getStatus()) && !CollectionUtils.isEmpty(resultVo.getProcessedImages())) {

                if (midjourneyService.promptRecordFinished(resultVo.getJobId(), resultVo.getLoginName())) {
                    return;
                }

                // 更新PromptRecord的结束时间
                midjourneyService.updatePromptRecordEndTime(resultVo.getJobId(), resultVo.getLoginName());

                // 处理成功，直接插入PromptFile记录到数据库
                insertPromptFilesFromProcessedImages(resultVo);
                // 清理Redis任务信息
                midjourneyService.cleanupTaskFromRedis(resultVo.getJobId(), resultVo.getLoginName());
                log.info("成功插入{}张图片记录到数据库，jobId: {}", resultVo.getProcessedImages().size(), resultVo.getJobId());
            } else {
                // 处理失败，记录错误日志
                log.error("图片处理失败，jobId: {}, errorMessage: {}", resultVo.getJobId(), resultVo.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("处理MJ图片处理结果失败，jobId: {}", resultVo.getJobId(), e);
        }
    }

    /**
     * 直接插入PromptFile记录到数据库（参考原来processTaskImages的模式）
     */
    private void insertPromptFilesFromProcessedImages(MjImageProcessResultVo resultVo) {
        String jobId = resultVo.getJobId();
        String loginName = resultVo.getLoginName();
        String markId = resultVo.getMarkId();

        // 更新用户当日生图数量
        Integer userTodayCreateImgNums = (Integer) redisService.getDataFromHash(USER_TODAY_CREATE_IMG_NUMS, loginName);
        userTodayCreateImgNums = userTodayCreateImgNums != null ? userTodayCreateImgNums : 0;
        redisService.putDataToHash(USER_TODAY_CREATE_IMG_NUMS, loginName, userTodayCreateImgNums + resultVo.getProcessedImages().size());

        // 保存图像文件信息到gpt_prompt_file表
        int successImageCount = 0;
        for (MjImageProcessResultVo.ProcessedImageInfo processedImage : resultVo.getProcessedImages()) {
            if ("SUCCESS".equals(processedImage.getProcessStatus())) {
                if (insertSinglePromptFile(jobId, loginName, processedImage)) {
                    successImageCount++;
                }
            } else {
                log.error("图片处理失败，jobId: {}, originalUrl: {}, errorMessage: {}",
                        jobId, processedImage.getOriginalUrl(), processedImage.getErrorMessage());
            }
        }

        // 更新VIP相关统计，参考GenService的callBack方法
        try {
            vipService.updateMessageByMarkId(markId, jobId, loginName, successImageCount, successImageCount, successImageCount);
        } catch (Exception e) {
            log.error("更新VIP统计信息失败，jobId: {}, user: {}", jobId, loginName, e);
        }

        log.info("Processed {} images for jobId: {}, user: {}", resultVo.getProcessedImages().size(), jobId, loginName);
    }

    /**
     * 插入单个PromptFile记录
     */
    private boolean insertSinglePromptFile(String jobId, String loginName, MjImageProcessResultVo.ProcessedImageInfo processedImage) {
        boolean checkNSFWSuccess = true;
        try {
            PromptFile promptFile = new PromptFile();
            promptFile.setLoginName(loginName);
            promptFile.setPromptId(jobId);

            String fileName = getFilePath(processedImage.getImgUrl());
            String thumbnailName = getFilePath(processedImage.getThumbnailUrl());
            String highThumbnailName = getFilePath(processedImage.getHighThumbnailUrl());

            // 从URL中提取文件名
            promptFile.setFileName(fileName);
            promptFile.setThumbnailName(thumbnailName);
            promptFile.setHighThumbnailName(highThumbnailName);

            // 设置各种图片URL（使用处理后的压缩图片URL）

            promptFile.setFileUrl(processedImage.getImgUrl());
            promptFile.setThumbnailUrl(processedImage.getThumbnailUrl());
            promptFile.setHighThumbnailUrl(processedImage.getHighThumbnailUrl());
            promptFile.setMiniThumbnailUrl(processedImage.getMiniThumbnailUrl());
            promptFile.setHighMiniUrl(processedImage.getHighMiniUrl());

            // 设置敏感信息
            promptFile.setSensitiveMessage(processedImage.getSensitive());
            if (StringUtils.isNotBlank(promptFile.getSensitiveMessage())) {
                checkNSFWSuccess = false;
            }

            // 设置图片尺寸
            promptFile.setWidth(processedImage.getWidth());
            promptFile.setHeight(processedImage.getHeight());

            // 设置文件大小
            if (processedImage.getSize() != null) {
                promptFile.setSize(processedImage.getSize());
            }

            promptFile.setCreateTime(LocalDateTime.now());
            promptFile.setCreateBy(loginName);

            promptFileMapper.insert(promptFile);
            log.info("Saved image file for jobId: {}, imgUrl: {}", jobId, processedImage.getImgUrl());

        } catch (Exception e) {
            log.error("插入PromptFile记录失败，jobId: {}, imgUrl: {}", jobId, processedImage.getImgUrl(), e);
        }

        return checkNSFWSuccess;
    }

}
