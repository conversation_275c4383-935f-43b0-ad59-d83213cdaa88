package com.lx.pl.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lx.pl.client.BackendApi;
import com.lx.pl.client.NdApi;
import com.lx.pl.constant.LockPrefixConstant;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.gen.entity.*;
import com.lx.pl.db.mysql.gen.mapper.*;
import com.lx.pl.dto.*;
import com.lx.pl.dto.NdResult.Detection;
import com.lx.pl.dto.generic.R;
import com.lx.pl.dto.mq.ReadyServerVo;
import com.lx.pl.enums.*;
import com.lx.pl.exception.BadRequestException;
import com.lx.pl.exception.LogicException;
import com.lx.pl.exception.OperationNotAllowedException;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.manager.PromptTemplateManager;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.util.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.NodeList;
import retrofit2.Response;

import javax.annotation.Resource;
import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.ImageWriter;
import javax.imageio.metadata.IIOInvalidTreeException;
import javax.imageio.metadata.IIOMetadata;
import javax.imageio.metadata.IIOMetadataFormatImpl;
import javax.imageio.metadata.IIOMetadataNode;
import javax.imageio.stream.ImageInputStream;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lx.pl.constant.LogicConstants.FAIL_MSG_SPLIT;
import static com.lx.pl.service.ImgUploadCommonService.getFilePath;
import static com.lx.pl.service.LoadBalanceService.*;
import static com.lx.pl.service.VipService.USER_TODAY_RELAX_CREATE_IMG_NUMS;

@Service
@Slf4j
public class GenService {

    @Value("${comfyui.host}")
    String comfyUIHost;

    @Value("${nd.url}")
    String ndSercieUrl;

    @Value("${image.storage.path.prefix}")
    String storagePathPrefix;

    @Value("${image.download.prefix}")
    String imageDownloadPrefix;

    @Value("${apikey}")
    String apikey;

    @Value("${callback_url}")
    String callback_url;

    @Value("${ai.create.folder}")
    String aiCreateFolder;

    @Value("${localRedraw.folder}")
    String localRedrawFolder;

    @Value("${thumbnail.ai.create.folder}")
    String thumbnailAiCreateFolder;

    @Value("${high.thumbnail.ai.create.folder}")
    String highThumbnailAiCreateFolder;

    @Value("${modelIds}")
    String modelIds;

    @Value("${wbMessage.max.retries}")
    Integer wbMessageMaxRetries;

    @Value("${opex.loginName}")
    String opexLoginName;

    @Value("${user.maxTaskNums}")
    Double userMaxTaskNums;

    @Value("${server.busyNums}")
    Double serverBusyNums;

    @Value("${realistic.ModelId}")
    String realisticModelId;

    @Value("${anime.ModelId}")
    String animeModelId;

    @Value("${lineart.ModelId}")
    String lineartModelId;

    @Value("${pony.ModelId}")
    String ponyModelId;

    @Value("${art.ModelId}")
    String artModelId;

    @Value("${fluxschell.modelId}")
    String fluxschellModelId;

    @Value("${fluxdev.modelId}")
    String fluxdevModelId;

    @Value("${ttapimj.modelId}")
    String ttapiMjModelId;

    @Value("${fluxKontextPro.modelId}")
    String fluxKontextProModelId;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    CommonService commonService;

    @Autowired
    UserPromptFileRelMapper userPromptFileRelMapper;

    @Autowired
    ImageGenRecordMapper imageGenRecordMapper;

    @Autowired
    RawPromptsMapper rawPromptsMapper;

    @Autowired
    WorkflowService workflowService;

    @Autowired
    UserService userService;

    @Autowired
    RedisService redisService;

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    RedisTemplate<String, Object> redisTemplate;

    String[] prompts;

    @Autowired
    BackendApi backendApi;

    @Autowired
    NdApi ndApi;

    @Autowired
    WebSocketServer webSocketServer;

    @Autowired
    PromptFileMapper promptFileMapper;

    @Autowired
    PromptRecordMapper promptRecordMapper;

    @Autowired
    ImgUploadCommonService imgUploadCommonService;

    @Autowired
    ExploreFileMapper exploreFileMapper;

    @Resource
    GeoIpCountryService geoIpCountryService;

    @Resource
    LoadBalanceService loadBalanceService;

    @Autowired
    VipService vipStandardsService;

    @Autowired
    SubscriptionCurrentService subscriptionCurrentService;

    @Autowired
    UserMapper userMapper;

    @Lazy
    @Autowired
    TaskService taskService;

    @Autowired
    PromptTemplateManager promptTemplateManager;

    @Autowired
    PromptFiltrationService promptFiltrationService;
    @Resource
    private LumenService lumenService;

    @Resource
    private GptModelAboutMapper gptModelAboutMapper;
    @Resource
    private ModelService modelService;


    // 使用 AtomicInteger 来维护当前的索引
    private final AtomicInteger currentIndex = new AtomicInteger(0);


    Map<String, Integer> countMap = new ConcurrentHashMap<>();

    Map<String, VipStandards> vipStandardMap = new ConcurrentHashMap<>();

    public static final String WsErrorMessagekey = "wsErrorMessage";

    public static final String ServerStatusListkey = "serverStatusList";

    public static final String USER_TODAY_CREATE_IMG_NUMS = "user_today_create_img_nums";

    public static final String IP_TODAY_RELAX_CREATE_IMG_NUMS = "ip_today_relax_create_img_nums";  //ip当天relax生图数量

    @NotNull
    private static String getNSFWFlag(NdResult ndResult) {
        List<Detection> detections = ndResult.getDetections();

        String nsfwFlag = "";
        for (Detection d : detections) {
            String type = d.getClassType();
            double score = d.getScore();
            if (type.contains("FEMALE_BREAST_EXPOSED") && score > 0.5) {
                // 女性乳房暴露
                nsfwFlag += "[女性乳房暴露]";
            } else if (type.contains("FEMALE_GENITALIA_EXPOSED") && score > 0.5) {
                // 女性生殖器暴露
                nsfwFlag += "[女性生殖器暴露]";
            } else if (type.contains("ANUS_EXPOSED") && score > 0.5) {
                // 肛门暴露
                nsfwFlag += "[肛门暴露]";
            } else if (type.contains("MALE_GENITALIA_EXPOSED") && score > 0.5) {
                // 男性生殖器暴露
                nsfwFlag += "[男性生殖器暴露]";
            }
        }
        return nsfwFlag;
    }

    public static List<Image> extractFilename(String json) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode jsonNode = objectMapper.readTree(json);
            JsonNode firstChild = jsonNode.elements().next();
            JsonNode imagesNode = firstChild.get("outputs").elements().next().get("images");

            List<Image> imageList = new ArrayList<>();

            for (JsonNode imageNode : imagesNode) {
                Image image = objectMapper.treeToValue(imageNode, Image.class);
                imageList.add(image);
            }

            return imageList;
        } catch (JsonProcessingException e) {
            throw new ServerInternalException("解析失败，生成可能还未完成");
        }
    }

    public String randomPrompt() {
        if (prompts == null) {
            QueryWrapper<RawPrompts> qw = new QueryWrapper();
            List<RawPrompts> rawPrompts = rawPromptsMapper.selectList(qw);
            prompts = new String[rawPrompts.size()];
            for (int i = 0; i < rawPrompts.size(); i++) {
                prompts[i] = rawPrompts.get(i).getContentJson();
            }
        }
        // 创建一个 Random 对象
        Random random = new Random();

        // 生成一个随机索引
        int randomIndex = random.nextInt(prompts.length);

        // 获取随机选择的字符串
        String randomPrompt = prompts[randomIndex];
        return randomPrompt;
    }

    public String prompt(GenGenericPara paras, User user) throws IOException {
        String url = comfyUIHost + "/prompt?client_id=" + user.getLoginName();
        log.info("gen info: {}", paras.toString());
        log.info("gen image init, invoke url: {}", url);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 设置请求体数据
        String requestBody = workflowService.loadWorkflow(paras, user.getLoginName());
        log.debug("workflow content: {}", requestBody);

        // 创建请求实体对象
        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

        // 发送POST请求并获取响应
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

        // 获取响应数据
        String responseBody = responseEntity.getBody();

        ObjectMapper om = new ObjectMapper();
        ComfyUIPromptResult comfyUIPromptResult = om.readValue(responseBody, ComfyUIPromptResult.class);

        String promptId = comfyUIPromptResult.getPrompt_id();

        // 先保存一下prompt id和生图信息
        ObjectMapper objectMapper = new ObjectMapper();
        ImageGenRecord i = new ImageGenRecord();
        i.setUserId(user.getId());
        i.setLoginName(user.getLoginName());
        i.setCreateTimestamp(LocalDateTime.now());
        i.setPromptId(promptId);
        i.setGenInfo(JsonUtils.writeToJsonNode(paras));
        imageGenRecordMapper.insert(i);

        return promptId;
    }

    public List<Image> history(String promptId, User user) {
        RestTemplate restTemplate = new RestTemplate();
        String url = comfyUIHost + "/history/" + promptId;
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        String responseString = response.getBody();
        log.info("comfyUI history response: {}", responseString);
        List<Image> fileNames = extractFilename(responseString);

        // 这里保存一下用户id，prompt id，文件名的关系
        for (Image image : fileNames) {
            UserPromptFileRel u = new UserPromptFileRel();
            u.setUserId(user.getId());
            u.setPromptId(promptId);
            u.setFileName(image.getFilename());
            userPromptFileRelMapper.insert(u);
        }

        return fileNames;
    }

    private Forge forgeSingleFile(String fileName) {
        RestTemplate restTemplate = new RestTemplate();
        String url;
        if (fileName.contains("/")) {
            String[] pathInfos = fileName.split("/");
            url = comfyUIHost + "/view?filename=" + pathInfos[1] + "&subfolder=" + pathInfos[0]
                    + "&type=output";
        } else {
            url = comfyUIHost + "/view?filename=" + fileName + "&type=output";
        }

        ResponseEntity<byte[]> response = restTemplate.getForEntity(url, byte[].class);

        byte[] imageData = response.getBody();

        // 在拿到图片的同时，保存到数据库中
        String filePath = storagePathPrefix + fileName;

        try {
            FileUtil.mkParentDirs(filePath);

            FileUtils.saveByteDataToFile(imageData, filePath);
            log.info("数据保存成功！{}", filePath);

            try {
                // 检测NSFW，加模糊效果
                String nsfwInfo = nd(filePath);
                if (!StringUtils.hasLength(nsfwInfo)) {
                    nsfwInfo = "[]"; // 未检测到nsfw信息，设置为[]
                }
                // 获取文件路径的Path对象
                Path path = Paths.get(filePath);
                // 获取文件名和父路径
                String fn = path.getFileName().toString();
                Path parentPath = path.getParent();
                // 去掉文件后缀名
                String fileNameWithoutExtension = fn.replaceFirst("[.][^.]+$", "");
                // 重新拼接文件路径
                String newPath = parentPath.resolve(fileNameWithoutExtension).toString();
                // 添加_s作为缩略图并加上后缀名
                String thumbnailFileName = newPath + "_s.webp";
                createThumbnail(filePath, thumbnailFileName);

                // 先通过fileName拿到prompt id
                QueryWrapper<UserPromptFileRel> w = new QueryWrapper<>();
                w.eq("file_name", fileName);
                UserPromptFileRel userPromptFileRel = userPromptFileRelMapper.selectOne(w);
                String promptId = userPromptFileRel.getPromptId();

                // 再通过promptId拿到生图记录并更新
                QueryWrapper<ImageGenRecord> recordWrapper = new QueryWrapper<>();
                recordWrapper.eq("prompt_id", promptId);
                ImageGenRecord imageGenRecord = imageGenRecordMapper.selectOne(recordWrapper);
                String oldFileNameVale = imageGenRecord.getFileName();
                if (StringUtils.hasLength(oldFileNameVale)) {
                    imageGenRecord.setFileName(oldFileNameVale + "|" + fileName);
                } else {
                    imageGenRecord.setFileName(fileName);
                }
                imageGenRecord.setLength(imageData.length);
                String oldNsfwInfo = imageGenRecord.getNsfwInfo();
                if (StringUtils.hasLength(oldNsfwInfo)) {
                    imageGenRecord.setNsfwInfo(oldNsfwInfo + "|" + nsfwInfo);
                } else {
                    imageGenRecord.setNsfwInfo(nsfwInfo);
                }
                imageGenRecordMapper.updateById(imageGenRecord);

                // 返回较小的缩略图
                Path p = Paths.get(thumbnailFileName);
                byte[] thumbNailContent = Files.readAllBytes(p);

                Forge f = new Forge();
                f.setFilename(fileName);
                f.setUrl(imageDownloadPrefix + fileName);
                f.setThumbnailUrl((imageDownloadPrefix + fileName).replace(".png", "_s.webp"));
                f.setNsfwInfo(nsfwInfo);
                f.setGenInfo(imageGenRecord.getGenInfo());
                return f;
            } catch (IOException e) {
                throw new ServerInternalException("检测图片NSFW失败");
            }
        } catch (IOException e) {
            log.info("保存数据时出错：" + e.getMessage());
        } catch (Exception e) {
            log.info("保存数据时发生异常：" + e.getMessage());
        }
        throw new ServerInternalException("数据未正确处理，请稍后再试");
    }

    private boolean createThumbnail(String inputImagePath, String outputImagePath) {
        log.info("生成缩略图, {}, {}", inputImagePath, outputImagePath);
        try {
            String os = System.getProperty("os.name").toLowerCase();
            Process process;
            if (os.contains("win")) {
                String command = "magick " + inputImagePath + " -quality 60 " + outputImagePath;
                log.info("缩略图生成命令: {}", command);
                process = Runtime.getRuntime().exec(command);
            } else {
                String command = "convert " + inputImagePath + " -quality 60 " + outputImagePath;
                log.info("缩略图生成命令: {}", command);
                process = Runtime.getRuntime().exec(command);
            }

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                log.info(line);
            }
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                log.info("生成缩略图，命令执行成功");
                return true;
            } else {
                log.info("生成缩略图，命令执行失败: exitCode {}", exitCode);
                return false;
            }
        } catch (IOException | InterruptedException e) {
            log.error("生成缩略图失败", e);
        }
        return false;
    }

    private String nd(String filePath) throws IOException {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        // body.add("image", imageData);
        body.add("image", new FileSystemResource(new File(filePath)));

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        String url = ndSercieUrl;

        String responseString = restTemplate.postForEntity(url, requestEntity, String.class).getBody();

        log.info("nd detect result: {}", responseString);
        responseString = responseString.replaceAll("\"class\"", "\"classType\"");

        ObjectMapper objectMapper = new ObjectMapper();
        NdResult ndResult = objectMapper.readValue(responseString, NdResult.class);
        String nsfwFlag = getNSFWFlag(ndResult);

        if (StringUtils.hasLength(nsfwFlag)) {
            // 检测到NSFW，需要处理
            log.info("检测到NSFW, 处理中... {}", filePath);
            boolean result = processAndSaveImage(filePath, filePath);
            log.info("处理完成，结果： {}", result);
            if (!result) {
                FileUtil.del(filePath);
                throw new ServerInternalException("处理NSFW图片失败，图片已直接删除，确保无法看到NSFW内容");
            }
        } else {
            log.info("未检测到NSFW内容");
        }

        return nsfwFlag;
    }

    public boolean processAndSaveImage(String inputImagePath, String outputImagePath) {
        try {
            String os = System.getProperty("os.name").toLowerCase();

            Process process;
            if (os.contains("win")) {
                String command = "magick " + inputImagePath + " -scale 2% -scale 5000% " + outputImagePath;
                process = Runtime.getRuntime().exec(command);
            } else {
                String command = "convert " + inputImagePath + " -scale 2% -scale 5000% " + outputImagePath;
                process = Runtime.getRuntime().exec(command);
            }

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                log.info(line);
            }
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                log.info("命令执行成功");
                return true;
            } else {
                log.info("命令执行失败");
                return false;
            }
        } catch (IOException | InterruptedException e) {
            log.error("处理缩略图失败", e);
        }
        return false;
    }

    public String getPlatform(HttpServletRequest request) {
        String platform = request.getHeader("Platform");
        if (StringUtil.isNotBlank(platform)) {
            return platform.toLowerCase();
        } else {
            return "";
        }
    }

    //校验模型是否是dev模型
    public Boolean checkDevModel(String modelId) {
        if (StringUtil.isBlank(modelId)) {
            return Boolean.FALSE;
        }

        if (fluxdevModelId.equals(modelId)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    //校验模型是否是ttapi mj模型
    public Boolean checkTtapiMjModel(String modelId) {
        if (StringUtil.isBlank(modelId)) {
            return Boolean.FALSE;
        }

        if (ttapiMjModelId.equals(modelId) || fluxKontextProModelId.equals(modelId)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    //校验模型是否是flux kontext pro模型
    public Boolean checkFluxKontextProModel(String modelId) {
        if (StringUtil.isBlank(modelId)) {
            return Boolean.FALSE;
        }

        if (fluxKontextProModelId.equals(modelId)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public void addRelaxCreateImgNumByIp(int imgNums, HttpServletRequest request) {
        String operateIp = IpUtils.getIpAddr(request);
        //根据ip进行relax数据埋点
        if (com.lx.pl.util.StringUtils.isNotBlank(operateIp)) {
            redisService.incrementFieldInHash(IP_TODAY_RELAX_CREATE_IMG_NUMS, operateIp, Long.valueOf(imgNums));
        }
    }

    /**
     * 当前平台只支持拥有的模型进行生图
     *
     * @param modelId
     * @param request
     * @return
     * @throws IOException
     */
    public Boolean checkPlatformModel(String modelId, HttpServletRequest request) throws IOException {
        if (StringUtil.isBlank(modelId)) {
            return Boolean.FALSE;
        }

        List<ModelInformation.ModelAbout> resultList = modelService.listModels(request.getHeader("Platform"));
        if (!CollectionUtils.isEmpty(resultList)) {
            Set modelIdSet = resultList.stream().map(modelAbout -> modelAbout.getModelId()).collect(Collectors.toSet());
            if (!modelIdSet.contains(modelId)) {
                return Boolean.FALSE;
            }
        }

        return Boolean.TRUE;
    }

    public boolean checkEditModel(String modelId) {
        if (StringUtil.isBlank(modelId)) {
            return false;
        }

        return Objects.equals(modelId, fluxKontextProModelId);
    }

    public Integer getRelaxWaitTime(String loginName, Boolean continueCreate, Boolean notVip, HttpServletRequest request) throws JsonProcessingException {
        // 获取用户当天relax图片数量，若为空则设置为0
        int imageCount = (int) Optional.ofNullable(redisService.getDataFromHash(USER_TODAY_RELAX_CREATE_IMG_NUMS, loginName)).orElse(0);

        //判断是否是特殊国家（GDP低于泰国）
        Boolean lowerThanThaiGDP = loadBalanceService.judgeTheIpAddress(request);

        int relaxWaitTime = 0;
        //处理非会员,根据ip来限制
        if (notVip) {
            String operateIp = IpUtils.getIpAddr(request);
            if (StringUtil.isNotBlank(operateIp)) {
                int ipImageCount = (int) Optional.ofNullable(redisService.getDataFromHash(IP_TODAY_RELAX_CREATE_IMG_NUMS, operateIp)).orElse(0);
                //根据用户和ip，取最大的延时等待时间
                imageCount = imageCount > ipImageCount ? imageCount : ipImageCount;
            }

            //Android审核临时控制
            if (!MyEnvironmentUtils.isStagingEnvironment()) {
                //非vip闲时生图达到40张，则做限制
                if (imageCount >= 40) {
                    log.info("用户：{},ip：{},relax图片数量达到40张，限制用户生成图片", loginName, operateIp);
                    throw new LogicException(LogicErrorCode.RELAX_CREATE_LIMIT);
                }
            }

        } else {

            //获取relax等待时间
            relaxWaitTime = loadBalanceService.getWaitTime(!lowerThanThaiGDP, imageCount);
        }

        //是否继续生图，任何国家等待时长超过300s
        if (!Objects.isNull(continueCreate) && !continueCreate && relaxWaitTime >= 300) {
            throw new LogicException(LogicErrorCode.CONTINUE_CREATE);
        }

        return relaxWaitTime;
    }


    /**
     * 统一生成图片接口，NOTE：调用py后端
     *
     * @param paras
     * @param user
     * @return
     * @throws IOException
     */
    public Map<String, Object> create(GenGenericPara paras, CreatePicParams createPicParams, User user) throws IOException {
        Boolean fastHour = createPicParams.getFastHour();
        String platform = createPicParams.getPlatform();
        String markId = createPicParams.getMarkId();
        int relaxWaitTime = createPicParams.getRelaxWaitTime();

        Map<String, Object> resultMap = new HashMap<>();
        // 设置请求体数据
        BackendPromptParams requestParam = new BackendPromptParams();
        BeanUtils.copyProperties(paras, requestParam);

        int width = paras.getResolution().getWidth();
        int height = paras.getResolution().getHeight();

        if ((width * height) > (1048576)) {
            throw new LogicException(LogicErrorCode.PIXELS_EXCEED_LIMIT);
        }

        //对超高生图的宽高进行处理
        if (createPicParams.getHighPixels()) {
            width = (int) Math.floor(width * 1.5);
            height = (int) Math.floor(height * 1.5);
        }

        requestParam.setGen_mode("quality");
        requestParam.setMark_id(markId);
        requestParam.setSampler(paras.getSampler_name());
        requestParam.setBatch_size(paras.getResolution().getBatch_size());
        requestParam.setWidth(width);
        requestParam.setHeight(height);
        requestParam.setPrompt(promptTemplateManager.getStyleHelperPrompt(paras.getMainCategory(), paras.getSubCategory(), paras.getPrompt()));
        requestParam.setCallback_url(callback_url);
        if (!Objects.isNull(paras.getMulti_img2img_info()) && !CollectionUtils.isEmpty(paras.getMulti_img2img_info().getStyle_list())) {
            BackendPromptParams.Img2img_info img2img_info = new BackendPromptParams.Img2img_info();
            List<GenGenericPara.StyleImg> style_list = paras.getMulti_img2img_info().getStyle_list();
            //对图生图参数进行校验
            judgePicToImgPara(style_list);
            img2img_info.setStyle_list(style_list);
            requestParam.setImg2img_info(img2img_info);
        }
        if (!Objects.isNull(paras.getImg_control_info()) && !CollectionUtils.isEmpty(paras.getImg_control_info().getStyle_list())) {
            BackendPromptParams.Img2img_info img2img_info = new BackendPromptParams.Img2img_info();
            List<GenGenericPara.StyleImg> style_list = paras.getImg_control_info().getStyle_list();
            //对图片控制参数进行校验
            validateStyleList(style_list, requestParam.getModel_id());
            img2img_info.setStyle_list(style_list);
            requestParam.setImg2img_info(img2img_info);
        }
        if (!Objects.isNull(paras.getModel_ability())) {
            BackendPromptParams.Hires_fix hiresFix = new BackendPromptParams.Hires_fix();
            //开启超清修复
            //SD1.5的模型出图默认超分2倍
            if (modelIds.contains(paras.getModel_id())) {
                hiresFix.setScale(2);
                hiresFix.setStatus(1);
            }
            BackendPromptParams.Extra_data extraData = new BackendPromptParams.Extra_data();
            extraData.setHires_fix(hiresFix);
            extraData.setAnime_style(paras.getModel_ability().getAnime_style_control());
            requestParam.setExtra_data(extraData);
        }

        //非英文则翻译成英文进行生图
        String englishPrompt = "";
//    if(paras.getTranslationFlag()){
//      englishPrompt = translationToEnglish(requestParam.getPrompt());
//      requestParam.setPrompt(englishPrompt);
//    }

        //判断生图类型
        String feature = loadBalanceService.judgeFeatures(paras);
        String pushQueueResult = "";

        Boolean notFastHour = Boolean.FALSE;
        try {
            //数据入库
            PromptRecord promptRecord = new PromptRecord();
            promptRecord.setLoginName(user.getLoginName());
            promptRecord.setGenInfo(JsonUtils.writeToJsonNode(paras));
            promptRecord.setGenMode(paras.getGen_mode());
            promptRecord.setPrompt(paras.getPrompt());
            promptRecord.setNegativePrompt(paras.getNegative_prompt());
            promptRecord.setBatchSize(paras.getResolution().getBatch_size());
            promptRecord.setModelId(paras.getModel_id());
            promptRecord.setCreateBy(user.getLoginName());
            promptRecord.setCreateTime(LocalDateTime.now());
            promptRecord.setAspectRatio(paras.getResolution().getWidth() + " * " + paras.getResolution().getHeight());
            //如果图生图有对象，不为空，且图片有值，则生图对象为图生图
            if (!Objects.isNull(paras.getMulti_img2img_info()) && !CollectionUtils.isEmpty(paras.getMulti_img2img_info().getStyle_list())) {
                promptRecord.setOriginCreate(OriginCreate.picCreate.getValue());
            } else {
                promptRecord.setOriginCreate(OriginCreate.create.getValue());
            }
            if (StringUtil.isNotBlank(englishPrompt)) {
                promptRecord.setEnglishPrompt(JsonUtils.writeToJsonNode(englishPrompt));
            }
            promptRecord.setPromptParams(JsonUtils.writeToJsonNode(requestParam));
            promptRecord.setMarkId(markId);
            promptRecord.setFeatureName(feature);

            //notFastHour = loadBalanceService.judgeTheFastHourExceede(user.getLoginName());
            //判断是否属于fastHour内的任务
            promptRecord.setFastHour(fastHour);
            promptRecord.setPlatform(platform);
            promptRecord.setMainCategory(paras.getMainCategory());
            promptRecord.setSubCategory(paras.getSubCategory());
            promptRecordMapper.insert(promptRecord);

            //记录用户生图状态到redis
            //redisService.putDataToHash(user.getLoginName(), markId, -1, 2, TimeUnit.HOURS);
            redisService.stringSet(markId, user.getLoginName(), 2, TimeUnit.HOURS);

            /**
             * relax任务，满足一定生图量，则进入relax等待队列
             */
            if ((relaxWaitTime > 0) && !fastHour) {
                //推送等待队列
                pushQueueResult = loadBalanceService.pushToRelaxWaitQueue(paras.getModel_id(), feature, markId, relaxWaitTime);
            } else {
                //推送到队列里
                pushQueueResult = loadBalanceService.pushToTaskQueue(paras.getModel_id(), feature, fastHour, markId, user);
            }

        } catch (Exception e) {
            log.error("create error", e);
            //任务状态改为失败
            updatePromptRecordStatus(markId, user.getLoginName());
        }

        /**
         * 判断进入黑名单是否有误
         */
        judgePushQueueError(pushQueueResult, markId, user.getLoginName());

        resultMap.put("markId", markId);
        resultMap.put("index", -1);
        resultMap.put("fastHour", fastHour);
        resultMap.put("featureName", feature);
        return resultMap;
    }

    //异步执行未完成的任务
    @Async
    public void dealNotFinishTash(String loginName) throws IOException {
        lumenService.notFinishTask(loginName);
    }

    /**
     * 校验图生图参数是否合规
     *
     * @param styleList
     */
    public void judgePicToImgPara(List<GenGenericPara.StyleImg> styleList) {
        Map<String, Long> styleCountMap = styleList.stream()
                .map(GenGenericPara.StyleImg::getStyle)
                .collect(Collectors.groupingBy(style -> style, Collectors.counting()));

        // 1. 检查是否有重复 style
        styleCountMap.forEach((style, count) -> {
            if (count > 1) {
                throw new BadRequestException("ImgToImg Illegal parameter");
            }
        });
    }

    public void validateStyleList(List<GenGenericPara.StyleImg> styleList, String modelId) {
        Set<String> set = Set.of("openposeControl");

        boolean containsOpenposeControl = styleList.stream()
                .anyMatch(styleImg -> set.contains(styleImg.getStyle()));

        Set<String> modelIdSet = Set.of(realisticModelId, animeModelId, ponyModelId);
        if (!containsOpenposeControl || !modelIdSet.contains(modelId)) {
            throw new BadRequestException("ImgToImg Illegal modelId");
        }

    }


    public void judgePushQueueError(String pushQueueResult, String markId, String userLoginName) {
        if (StringUtil.isBlank(pushQueueResult)) {
            return;
        }

        if (BLFLUX_QUEUE_KEY_ERROR.equals(pushQueueResult) || BLCOMMON_QUEUE_KEY_ERROR.equals(pushQueueResult)) {
            try {
                //删除失败的任务
                loadBalanceService.dealFailureTask(markId, "", userLoginName, "Task Failed");
            } catch (JsonProcessingException e) {
                log.error("删除失败的任务 dealFailureTask", e);
            }
            throw new BadRequestException("There are too many people in the queue at the moment. Please try again later!");
        }
    }


//    public Boolean judgeTheSpecialRule(HttpServletRequest request, String loginName) throws JsonProcessingException {
//        return loadBalanceService.judgeTheIpAddress(request) || loadBalanceService.judgeTheFastHourExceede(loginName);
//    }

    public List<ServerStatus> getServerStatus() throws JsonProcessingException {
        List<ServerStatus> serverStatusList = new ArrayList<>();
        String serverStatusJson = redisService.stringGet(ServerStatusListkey);
        if (StringUtil.isNotBlank(serverStatusJson)) {
            serverStatusList = JsonUtils.writeToList(serverStatusJson, ServerStatus.class);
            return serverStatusList;
        }

        log.debug("获取服务器列表请求参数: {}", "");
        Response<List<ServerStatus>> responseBody = backendApi.getServerStatus();
        log.info("调用Backend后端返回服务器列表信息: {}", JsonUtils.writeToString(responseBody.body()));
        // 获取响应数据
        List<ServerStatus> serverStatuses = responseBody.body();

        if (!CollectionUtils.isEmpty(serverStatuses)) {
            redisService.stringSet(ServerStatusListkey, JsonUtils.writeToString(serverStatuses), 1, TimeUnit.MINUTES);
            serverStatusList = serverStatuses;
        }
        return serverStatusList;
    }

    public void updatePromptRecordStatus(String markId, String loginName) {
        //任务状态改为失败
        LambdaUpdateWrapper<PromptRecord> luw = new LambdaUpdateWrapper();
        luw.eq(PromptRecord::getMarkId, markId);
        luw.eq(PromptRecord::getLoginName, loginName);
        luw.eq(PromptRecord::getUpdateTime, LocalDateTime.now());
        luw.set(PromptRecord::getDel, Boolean.TRUE);
        promptRecordMapper.update(null, luw);
    }

    /**
     * 校验用户的并发任务数是否超过额度
     *
     * @param user
     * @return
     */
    public Boolean checkUserConcurrentJobs(User user, String markId) {
        return checkUserConcurrentJobs(user, markId, Boolean.TRUE);
    }

    /**
     * 校验用户的并发任务数是否超过额度
     *
     * @param user
     * @return
     */
    public Boolean checkUserConcurrentJobs(User user, String markId, Boolean isSetHash) {
        RLock lock = redissonClient.getLock(LockPrefixConstant.CONCURRENT_EXECUTION_LOCK_PREFIX + user.getId());
        try {
            lock.lock();
            Boolean result = Boolean.TRUE;

            List<String> userNotFinishTaskList = redisService.getAllKeysFromHash(user.getLoginName());

            //如果用户的生图任务列表为空，则直接返回true
            if (CollectionUtils.isEmpty(userNotFinishTaskList)) {
                result = Boolean.FALSE;
            } else {
                VipStandards vipStandards = getVipStandards(user);

                //如果已经存在的生图任务数大或于等于会员批量生图数标准，则直接返回false
                if (vipStandards.getConcurrentJobs() > userNotFinishTaskList.size()) {
                    result = Boolean.FALSE;
                }
            }
            // 如果是调用第三方接口，则不设置hash
            if (!result && isSetHash) {
                redisService.putDataToHash(user.getLoginName(), markId, -1, 2, TimeUnit.HOURS);
            }

            return result;
        } catch (Exception e) {
            log.error("checkUserConcurrentJobs", e);
            throw new RuntimeException("checkUserConcurrentJobs Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public VipStandards getVipStandards(User user) {
        if (Objects.isNull(user)) {
            return null;
        }

        VipStandards vipStandards = vipStandardMap.get(user.getVipType());

        //如果用户的会员批量生图数标准信息为空，则重新查询会员标准信息
        if (Objects.isNull(vipStandards)) {
            List<VipStandards> vipStandardsList = vipStandardsService.getVipStandardsList(user);
            vipStandardMap = vipStandardsList.stream()
                    .collect(Collectors.toMap(VipStandards::getVipType, Function.identity()));

            vipStandards = vipStandardMap.get(user.getVipType());
        }
        return vipStandards;
    }

    public Boolean callBack(R<BackendCallBackParams> callBackParam) throws IOException {
        log.info("生图回调信息参数: {}", JsonUtils.writeToString(callBackParam));

        //如果状态码未成功，或者没有实际数据返回，则直接返回调用结果
        if (Objects.isNull(callBackParam.getData())) {
            return Boolean.TRUE;
        }

        BackendCallBackParams backParams = callBackParam.getData();
        BackendCallBackParams.Result result = backParams.getResult();

        //失败的数据直接处理
        if (!Objects.isNull(result) && !ComfyStatusCodeEnum.SUCCESS.getCode().equals(callBackParam.getStatus()) && PromptStatus.failure.getValue().equalsIgnoreCase(result.getPrompt_status())) {
            //获取未完成任务信息
            if (StringUtil.isNotBlank(result.getPrompt_id())) {
                String mark_id = (String) redisService.get(result.getPrompt_id());
                if (StringUtil.isBlank(mark_id)) {
                    return Boolean.TRUE;
                }
                String userLoginName = redisService.stringGet(mark_id);
                return dealFailure(callBackParam, mark_id, userLoginName);
            }
        }

        //生图成功数据,result有实际数据
        if (!Objects.isNull(result) && !CollectionUtils.isEmpty(result.getImgMessageList())) {
            return dealResult(callBackParam);
        }

        return Boolean.TRUE;
    }

    /**
     * 清楚redis对应的markId对应的信息
     */
    void cleanRedisByMarkId(String markId, String promptId, String loginName) {
        String serverId = (String) redisService.getDataFromHash(LogicConstants.MARKID_SERVERID_LIST, markId);
        if (redisService.listRemoveValue(serverId, markId) > 0) {
            redisService.comfySizeDecr(LogicConstants.COMFY_SIZE_PREFIX + serverId);
        }
        try {
            //移除未完成任务的状态
            redisService.deleteFieldFromHash(loginName, markId);
            //移除markId对应的队列名称
            redisService.deleteFieldFromHash(MARK_ID_QUEUE_NAME, markId);
            redisService.delete(promptId);
        } catch (Exception e) {
            log.error("移除markId对应的队列名称", e);
        }
        //更新用户任务时间
        Long userFinishTaskTimes = System.currentTimeMillis();
        Long userTaskTimes = (Long) redisService.get(USER_TASK_TIMESTAMP + markId);
        if (!Objects.isNull(userTaskTimes) && userFinishTaskTimes > userTaskTimes) {
            //删除不用的时间段和时间戳
            try {
                redisService.delete(USER_TASK_TIMESTAMP + markId);
            } catch (Exception e) {
                log.error("cleanRedisByMarkId 删除不用的时间段和时间戳", e);
            }

            //删除对应生成记录redis记录
            redisService.delete(PROMPT_RECORD_DATA + "_" + markId);
        }
    }

    /**
     * 修改redis就绪状态
     *
     * @param readyServerVo
     */
    public void dealComfyReadyStatus(ReadyServerVo readyServerVo) {
        String serverId = readyServerVo.getServerId();
        String promptId = readyServerVo.getPromptId();
        if (Boolean.TRUE.equals(readyServerVo.getReady()) && StringUtil.isNotBlank(serverId)) {
            if (StringUtil.isNotBlank(promptId)) {
                //获取未完成任务信息
                String markId = (String) redisService.get(promptId);
                if (StrUtil.isNotBlank(markId)) {
                    if (redisService.listRemoveValue(serverId, markId) > 0) {
                        redisService.comfySizeDecr(LogicConstants.COMFY_SIZE_PREFIX + serverId);
                    }
                    log.info("移除排队信息: {}", markId);
                }
            }
        }
    }

    public Boolean dealResult(R<BackendCallBackParams> callBackParam) throws IOException {
        BackendCallBackParams backParams = callBackParam.getData();
        log.info("生图回调任务成功数据: {}", JsonUtils.writeToString(backParams.getResult()));
        BackendCallBackParams.Result result = backParams.getResult();

        //生图任务成功，则移除用户的生图任务记录
        if (StringUtil.isNotBlank(result.getPrompt_id()) &&
                PromptStatus.success.getValue().equalsIgnoreCase(result.getPrompt_status())) {
            String userLoginName = "";
            String mark_id = "";
            try {
                //获取未完成任务信息
                mark_id = (String) redisService.get(result.getPrompt_id());
                if (StringUtil.isBlank(mark_id)) {
                    return Boolean.TRUE;
                }

                userLoginName = redisService.stringGet(mark_id);
                if (StringUtil.isBlank(userLoginName)) {
                    //对于生图成功的任务重复收到，则不消费
                    return Boolean.TRUE;
                } else {
                    if (Objects.isNull(redisService.getDataFromHash(userLoginName, mark_id))) {
                        //对于生图成功的任务重复收到，则不消费
                        return Boolean.TRUE;
                    }
                    cleanRedisByMarkId(mark_id, result.getPrompt_id(), userLoginName);
                }

                log.info("修改未任务完成状态完成,mark_id:{}", mark_id);

                //对获取到的结果图片进行处理
                if (!CollectionUtils.isEmpty(result.getImgMessageList())) {
                    List<BackendCallBackParams.ImgMessage> img_urls = result.getImgMessageList();

                    //更新用户当日生图数量
                    Integer userTodayCreateImgNums = (Integer) redisService.getDataFromHash(USER_TODAY_CREATE_IMG_NUMS, userLoginName);
                    userTodayCreateImgNums = !Objects.isNull(userTodayCreateImgNums) ? userTodayCreateImgNums : 0;
                    redisService.putDataToHash(USER_TODAY_CREATE_IMG_NUMS, userLoginName, userTodayCreateImgNums + img_urls.size());

                    int efficientImgNum = 0;
                    int highEfficientCost = 0;
                    for (BackendCallBackParams.ImgMessage imgUrl : img_urls) {
                        String fileName = getFilePath(imgUrl.getImg_url());
                        String thumbnailName = getFilePath(imgUrl.getThumbnail_url());
                        String highThumbnailName = getFilePath(imgUrl.getHigh_thumbnail_url());

                        log.info("获取py后端的图片信息,mark_id:{}，图片名称:{}", mark_id, fileName);

                        //图片信息入库
                        PromptFile promptFile = new PromptFile();
                        promptFile.setLoginName(userLoginName);
                        promptFile.setPromptId(result.getPrompt_id());

                        promptFile.setFileName(fileName);
                        promptFile.setThumbnailName(thumbnailName);
                        promptFile.setHighThumbnailName(highThumbnailName);

                        promptFile.setFileUrl(imgUrl.getImg_url());
                        promptFile.setThumbnailUrl(imgUrl.getThumbnail_url());
                        promptFile.setHighThumbnailUrl(imgUrl.getHigh_thumbnail_url());
                        promptFile.setMiniThumbnailUrl(imgUrl.getMiniThumbnailUrl());
                        promptFile.setHighMiniUrl(imgUrl.getHighMiniUrl());
                        promptFile.setSize(imgUrl.getSize());

                        promptFile.setWidth(imgUrl.getWidth());
                        promptFile.setHeight(imgUrl.getHeight());
                        if (StringUtil.isNotBlank(imgUrl.getSensitive())) {
                            promptFile.setSensitiveMessage(SensitiveMessage.NSFW.getValue());
                        } else {
                            //非马赛克图片，才算做有效图片数量 + 1
                            efficientImgNum++;
                            //计算总像素在200W以下，200W-300W，300W-400W，400W以上的数量
                            highEfficientCost += LogicUtil.calculateCostLumenByPixel(imgUrl.getWidth(), imgUrl.getHeight());
                        }
                        promptFile.setCreateTime(LocalDateTime.now());
                        promptFile.setCreateBy(userLoginName);
                        promptFileMapper.insert(promptFile);
                        log.info("图片进行入库完成 mark_id:{}，图片名称:{}", mark_id, fileName);
                    }
                    vipStandardsService.updateMessageByMarkId(mark_id, result.getPrompt_id(), userLoginName, img_urls.size(), efficientImgNum, highEfficientCost);
                }
            } catch (Exception e) {
                loadBalanceService.dealFailureTask(mark_id, result.getPrompt_id(), userLoginName, "Task Failed");
                log.error("生图回调信息成功，但解析数据失败，失败报错信息", e);
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 更新对应的任务信息
     *
     * @param markId
     * @param promptId
     * @param userLoginName
     * @param imgUrlListSize
     */
    void updateMessageByMarkId(String markId, String promptId, String userLoginName, int imgUrlListSize) {
        //将生图结束时间进行入库
        LambdaUpdateWrapper<PromptRecord> luw = new LambdaUpdateWrapper<PromptRecord>();
        luw.eq(PromptRecord::getMarkId, markId);
        luw.eq(PromptRecord::getLoginName, userLoginName);
        luw.set(PromptRecord::getPromptId, promptId);
        luw.set(PromptRecord::getGenEndTime, LocalDateTime.now());
        promptRecordMapper.update(null, luw);

        LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(User::getLoginName, userLoginName)
                .setSql("total_img_num = total_img_num + " + imgUrlListSize);

        userMapper.update(null, updateWrapper);
    }


    class TrySendWsRun implements Runnable {
        private final String userLoginName;
        private final String markId;

        public TrySendWsRun(String userLoginName, String markId) {
            this.userLoginName = userLoginName;
            this.markId = markId;
        }

        // 无返回值
        @Override
        public void run() {
            trySendWsErrorMessage(userLoginName, markId);
            Integer current = countMap.containsKey(userLoginName + markId) ? countMap.get(userLoginName + markId) : 0;
            countMap.put(userLoginName + markId, current + 1);
            if ((current + 1) < wbMessageMaxRetries) {
                // 重新调度任务
                TimeWheelTool.scheduleTask(userLoginName + markId, new TrySendWsRun(userLoginName, markId), 3, TimeUnit.SECONDS);
            } else {
                //清楚缓存数据
                redisService.deleteFieldFromHash(WsErrorMessagekey, userLoginName + markId);
                //数据入库
                LambdaUpdateWrapper<PromptRecord> luw = new LambdaUpdateWrapper();
                luw.eq(PromptRecord::getLoginName, userLoginName);
                luw.eq(PromptRecord::getMarkId, markId);
                luw.set(PromptRecord::getSendWsFailure, Boolean.TRUE);
                luw.set(PromptRecord::getUpdateTime, LocalDateTime.now());
                luw.set(PromptRecord::getUpdateBy, userLoginName);
                promptRecordMapper.update(null, luw);
            }
        }

    }

    @Async
    public void dealWsErrorMessage(String userLoginName, String markId, String wsCallBackJson) {
        //将发送websocket失败的数据存到redis
        String field = userLoginName + markId;
        redisService.putDataToHash(WsErrorMessagekey, field, wsCallBackJson);
        redisService.expire(WsErrorMessagekey, 60, TimeUnit.SECONDS);
    }

    /**
     * 重推websocket失败的消息
     *
     * @param userLoginName
     * @param markId
     */
    public void trySendWsErrorMessage(String userLoginName, String markId) {
        //重新发送websocket失败的数据
        String field = userLoginName + markId;
        String wsCallBackJson = (String) redisService.getDataFromHash(WsErrorMessagekey, field);

        webSocketServer.sendToOne(userLoginName, wsCallBackJson);
    }


    public Boolean dealFailure(R<BackendCallBackParams> callBackParams, String mark_id, String userLoginName) throws JsonProcessingException {
        BackendCallBackParams backParams = callBackParams.getData();
        BackendCallBackParams.Result result = backParams.getResult();
        loadBalanceService.dealFailureTask(mark_id, result.getPrompt_id(), userLoginName, callBackParams.getStatus() + FAIL_MSG_SPLIT + callBackParams.getMessage());
        return Boolean.TRUE;
    }


    /**
     * 延时操作
     */
    public void waitOneSecond() {
        try {
            Thread.sleep(1000); // 延时1秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    public String ndPicture(File file) throws IOException {

        // 准备文件
        RequestBody requestFile = RequestBody.create(okhttp3.MediaType.parse("multipart/form-data"), file);
        MultipartBody.Part filePart = MultipartBody.Part.createFormData("files", file.getName(), requestFile);

        //鉴黄失败，则当做黄图处理
        try {
            Response<R<List<NsfwResult>>> response = ndApi.ndPicture(filePart);

            // 获取响应数据
            R<List<NsfwResult>> result = response.body();
            log.info("鉴黄结果为: {} ，图片名称为：{}", JsonUtils.writeToString(result), file.getName());
            if (0 != result.getStatus() || CollectionUtils.isEmpty(result.getData())) {
                log.error("图片鉴黄失败，图片名称: {}", file.getName());
                return "nsfwError";
            }

            List<NsfwResult> nsfwResultList = result.getData();
            String nsfwFlag = getNSFWFlag(nsfwResultList.get(0));
            return nsfwFlag;
        } catch (Exception e) {
            log.error("图片鉴黄异常，图片名称: {}", file.getName(), e);
            return "nsfwError";
        }
    }

    @NotNull
    private static String getNSFWFlag(NsfwResult nsfwResult) {
        String nsfwFlag = "";
        if (nsfwResult.getFemaleBreastExposed() > 0.5) {
            // 女性乳房暴露
            nsfwFlag += "[女性乳房暴露]";
        }
        if (nsfwResult.getFemaleGenitaliaExposed() > 0.5) {
            // 女性生殖器暴露
            nsfwFlag += "[女性生殖器暴露]";
        }
        if (nsfwResult.getAnusExposed() > 0.5) {
            // 肛门暴露
            nsfwFlag += "[肛门暴露]";
        }
        if (nsfwResult.getMaleGenitaliaExposed() > 0.5) {
            // 男性生殖器暴露
            nsfwFlag += "[男性生殖器暴露]";
        }

        return nsfwFlag;
    }

    public NotFinishTaskResult getNotFinishTaskResult(User user) throws IOException {
        NotFinishTaskResult result = new NotFinishTaskResult();
        try {
            result.setConcurrentJobList(lumenService.notFinishTask(user.getLoginName()));
            result.setPreloadTaskQueue(taskService.getUserTaskQueue(user));
            //非会员则清除预载列表
            if (VipType.basic.getValue().equals(user.getVipType()) && !CollectionUtils.isEmpty(result.getPreloadTaskQueue())) {
                List<String> taskQueueIds = result.getPreloadTaskQueue().stream().map(UserTaskQueue::getId).collect(Collectors.toList());
                taskService.deleteUserTaskQueue(taskQueueIds, user);
                result.setPreloadTaskQueue(new ArrayList<>());
            }
        } catch (Exception e) {
            log.error("获取未完成任务失败，失败原因：", e);
        }
        return result;
    }


    public Map<String, Object> rmBackgroundPicture(DealImgParams dealImgParams, CreatePicParams createPicParams, User user) throws IOException {
        Boolean fastHour = createPicParams.getFastHour();
        String platform = createPicParams.getPlatform();
        String markId = createPicParams.getMarkId();
        int relaxWaitTime = createPicParams.getRelaxWaitTime();

        /**
         * promptId 由java自己生成，与前端进行交换，调用python为markId
         */
        String originPromptId = dealImgParams.getPromptId();

        //查询原始图片数据
        LambdaQueryWrapper<PromptRecord> qw = new LambdaQueryWrapper<PromptRecord>();
        qw.eq(PromptRecord::getPromptId, originPromptId);
        if (StringUtil.isNotBlank(dealImgParams.getLoginName())) {
            qw.eq(PromptRecord::getLoginName, dealImgParams.getLoginName());
        }
        PromptRecord originPromptRecord = promptRecordMapper.selectOne(qw);

        if (Objects.isNull(originPromptRecord)) {
            log.error("查询不到原始图片的相关信息，图片promptId：{}，用户名称：{}", originPromptId, user.getLoginName());
            throw new OperationNotAllowedException("upscale failed !");
        }

        // 设置请求体数据
        RembgParams rembgParams = new RembgParams();
        rembgParams.setType("rmbg_general");
        rembgParams.setImg_url(dealImgParams.getImgUrl());
        rembgParams.setPrompt_id(originPromptId);
        rembgParams.setMark_id(markId);
        rembgParams.setCallback_url(callback_url);

        /**
         * 根据taskId去数据库查询相关的数据
         */
        LambdaQueryWrapper<PromptFile> pfw = new LambdaQueryWrapper<PromptFile>();
        pfw.eq(PromptFile::getPromptId, originPromptId);
        pfw.eq(PromptFile::getFileUrl, dealImgParams.getImgUrl());
        if (StringUtil.isNotBlank(dealImgParams.getLoginName())) {
            pfw.eq(PromptFile::getLoginName, dealImgParams.getLoginName());
        }
        //收藏的图片，不会被物理删除
        //pfw.eq(PromptFile::getDel, Boolean.FALSE);
        PromptFile promptFile = promptFileMapper.selectOne(pfw);

        if (Objects.isNull(promptFile)) {
            throw new OperationNotAllowedException("Current image deleted, background removal failed !");
        }

        //数据入库
        PromptRecord promptRecord = new PromptRecord();
        promptRecord.setLoginName(user.getLoginName());
        promptRecord.setGenInfo(originPromptRecord.getGenInfo());
        promptRecord.setPrompt(originPromptRecord.getPrompt());
        promptRecord.setNegativePrompt(originPromptRecord.getNegativePrompt());
        promptRecord.setCreateBy(user.getLoginName());
        promptRecord.setCreateTime(LocalDateTime.now());
        promptRecord.setOriginCreate(OriginCreate.removeBackground.getValue());
        promptRecord.setBatchSize(1);
        promptRecord.setModelId(originPromptRecord.getModelId());
        promptRecord.setMarkId(markId);
        promptRecord.setAspectRatio(originPromptRecord.getAspectRatio());
        promptRecord.setPromptParams(JsonUtils.writeToJsonNode(rembgParams));
        promptRecord.setFeatureName(FeaturesType.removeBg.getValue());

        //Boolean notFastHour = loadBalanceService.judgeTheFastHourExceede(user.getLoginName());
        //判断是否属于fastHour内的任务
        promptRecord.setFastHour(fastHour);
        promptRecord.setPlatform(platform);
        promptRecordMapper.insert(promptRecord);

        //记录用户生图状态到redis
        //redisService.putDataToHash(user.getLoginName(), markId, -1, 2, TimeUnit.HOURS);
        redisService.stringSet(markId, user.getLoginName(), 2, TimeUnit.HOURS);

        /**
         * relax任务，满足一定生图量，则进入relax等待队列
         */
        String pushQueueResult = "";
        if ((relaxWaitTime > 0) && !fastHour) {
            //推送等待队列
            pushQueueResult = loadBalanceService.pushToRelaxWaitQueue(originPromptRecord.getModelId(), "removeBg", markId, relaxWaitTime);
        } else {
            //推送到队列里
            pushQueueResult = loadBalanceService.pushToTaskQueue(originPromptRecord.getModelId(), "removeBg", fastHour, markId, user);
        }

        /**
         * 判断进入黑名单是否有误
         */
        judgePushQueueError(pushQueueResult, markId, user.getLoginName());

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("markId", markId);
        resultMap.put("index", -1);
        resultMap.put("fastHour", fastHour);
        resultMap.put("featureName", promptRecord.getFeatureName());
        return resultMap;
    }

    public Map<String, Object> hiresFixPicture(DealImgParams dealImgParams, CreatePicParams createPicParams, User user) throws IOException {
        Boolean fastHour = createPicParams.getFastHour();
        String platform = createPicParams.getPlatform();
        String markId = createPicParams.getMarkId();
        int relaxWaitTime = createPicParams.getRelaxWaitTime();

        String originPromptId = dealImgParams.getPromptId();

        //查询原始图片数据
        LambdaQueryWrapper<PromptRecord> qw = new LambdaQueryWrapper<PromptRecord>();
        qw.eq(PromptRecord::getPromptId, originPromptId);
        if (StringUtil.isNotBlank(dealImgParams.getLoginName())) {
            qw.eq(PromptRecord::getLoginName, dealImgParams.getLoginName());
        }
        PromptRecord originPromptRecord = promptRecordMapper.selectOne(qw);

        if (Objects.isNull(originPromptRecord)) {
            log.error("查询不到原始图片的相关信息，图片promptId：{}，用户名称：{}", originPromptId, user.getLoginName());
            throw new OperationNotAllowedException("upscale failed !");
        }

        /**
         * promptId 由java自己生成，与前端进行交换，调用python为markId
         */
        // 设置请求体数据
        HiresFixParams hiresFixParams = new HiresFixParams();
        UpscaleParams upscaleParams = new UpscaleParams();

        /**
         * 上传，扩图，局部重绘，线稿上色，都可进行超分
         */
        Set<String> validValues = Set.of(
                OriginCreate.customUpload.getValue(),
                OriginCreate.enlargeImage.getValue(),
                OriginCreate.removeBackground.getValue(),
                OriginCreate.lineRecolor.getValue(),
                OriginCreate.localRedraw.getValue()
        );

        Boolean beEnlargeUpscale = validValues.contains(originPromptRecord.getOriginCreate());

        if (checkTtapiMjModel(dealImgParams.getModelId())) {
            beEnlargeUpscale = Boolean.TRUE;
        }
        String feature = "";
        //非上传图片超分和上传图片超分调用不同的接口
        if (!beEnlargeUpscale) {
            GenGenericPara genGenericPara = JsonUtils.fromJsonNode(originPromptRecord.getGenInfo(), GenGenericPara.class);

            hiresFixParams.setScale_by(dealImgParams.getScale());
            hiresFixParams.setImg_url(dealImgParams.getImgUrl());
            hiresFixParams.setPrompt_id(originPromptId);
            hiresFixParams.setCallback_url(callback_url);
            hiresFixParams.setMark_id(markId);
            hiresFixParams.setHd_denoise(dealImgParams.getDenoise());
            hiresFixParams.setPrompt(genGenericPara.getPrompt());
            hiresFixParams.setSeed(genGenericPara.getSeed());
            hiresFixParams.setNegative_prompt(genGenericPara.getNegative_prompt());
            hiresFixParams.setSampler(genGenericPara.getSampler_name());
            hiresFixParams.setScheduler(genGenericPara.getScheduler());
            hiresFixParams.setSteps(genGenericPara.getSteps());
            hiresFixParams.setCfg(genGenericPara.getCfg());
            hiresFixParams.setDenoise(genGenericPara.getDenoise());
            hiresFixParams.setHeight(genGenericPara.getResolution().getHeight());
            hiresFixParams.setWidth(genGenericPara.getResolution().getWidth());
            hiresFixParams.setModel_id(genGenericPara.getModel_id());

            feature = FeaturesType.upscale.getValue();
        } else {
            upscaleParams.setType("upscale_normal");
            upscaleParams.setScale_by(dealImgParams.getScale());
            upscaleParams.setImg_url(dealImgParams.getImgUrl());
            upscaleParams.setMark_id(markId);

            feature = FeaturesType.enlargeUpscale.getValue();
        }

        LambdaQueryWrapper<PromptFile> pfw = new LambdaQueryWrapper<PromptFile>();
        pfw.eq(PromptFile::getPromptId, originPromptId);
        pfw.eq(PromptFile::getFileUrl, dealImgParams.getImgUrl());
        if (StringUtil.isNotBlank(dealImgParams.getLoginName())) {
            pfw.eq(PromptFile::getLoginName, dealImgParams.getLoginName());
        }
        //pfw.eq(PromptFile::getDel, Boolean.FALSE);
        PromptFile promptFile = promptFileMapper.selectOne(pfw);

        if (Objects.isNull(promptFile)) {
            throw new OperationNotAllowedException("upscale failed !");
        }

        //数据入库
        PromptRecord promptRecord = new PromptRecord();
        promptRecord.setLoginName(user.getLoginName());
        promptRecord.setGenInfo(originPromptRecord.getGenInfo());
        promptRecord.setPrompt(originPromptRecord.getPrompt());
        promptRecord.setNegativePrompt(originPromptRecord.getNegativePrompt());
        promptRecord.setCreateBy(user.getLoginName());
        promptRecord.setCreateTime(LocalDateTime.now());
        promptRecord.setOriginCreate(OriginCreate.hiresFix.getValue());
        promptRecord.setBatchSize(1);
        promptRecord.setModelId(originPromptRecord.getModelId());
        promptRecord.setMarkId(markId);
        promptRecord.setAspectRatio(originPromptRecord.getAspectRatio());
        promptRecord.setPromptParams(!beEnlargeUpscale ? JsonUtils.writeToJsonNode(hiresFixParams) : JsonUtils.writeToJsonNode(upscaleParams));
        promptRecord.setFeatureName(!beEnlargeUpscale ? FeaturesType.upscale.getValue() : FeaturesType.enlargeUpscale.getValue());
        //Boolean notFastHour = loadBalanceService.judgeTheFastHourExceede(user.getLoginName());
        //判断是否属于fastHour内的任务
        promptRecord.setFastHour(fastHour);
        promptRecord.setPlatform(platform);
        promptRecordMapper.insert(promptRecord);

        //记录用户生图状态到redis
//        redisService.putDataToHash(user.getLoginName(), markId, -1, 2, TimeUnit.HOURS);
        redisService.stringSet(markId, user.getLoginName(), 2, TimeUnit.HOURS);

        /**
         * relax任务，满足一定生图量，则进入relax等待队列
         */
        String pushQueueResult = "";
        if ((relaxWaitTime > 0) && !fastHour) {
            //推送等待队列
            pushQueueResult = loadBalanceService.pushToRelaxWaitQueue(originPromptRecord.getModelId(), feature, markId, relaxWaitTime);
        } else {
            //推送到队列里
            pushQueueResult = loadBalanceService.pushToTaskQueue(originPromptRecord.getModelId(), feature, fastHour, markId, user);
        }

        /**
         * 判断进入黑名单是否有误
         */
        judgePushQueueError(pushQueueResult, markId, user.getLoginName());

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("markId", markId);
        resultMap.put("index", -1);
        resultMap.put("fastHour", fastHour);
        resultMap.put("featureName", promptRecord.getFeatureName());

        return resultMap;
    }

    public String getPromptStatusByIndex(String index) {
        switch (index) {
            case "-1":
                return PromptStatus.newbuilt.getValue();
            case "0":
                return PromptStatus.running.getValue();
            default:
                return PromptStatus.pending.getValue();
        }
    }


    public Map<String, Object> localRedraw(GenGenericPara genGenericPara, MultipartFile redrawImg, CreatePicParams createPicParams, User user) throws IOException {
        Boolean fastHour = createPicParams.getFastHour();
        String platform = createPicParams.getPlatform();
        String markId = createPicParams.getMarkId();
        int relaxWaitTime = createPicParams.getRelaxWaitTime();

        File file = FileUtils.transferToFile(redrawImg, "localRedrawImg");

        // //上传至存储桶的名字
        // String KEY = localRedrawFolder + file.getName();
        // /*获取签名图片的url*/
        // String signedUrl = cosService.getSignUrl(KEY);
        //
        // //保存数据到腾讯云
        // PutObjectResult putObjectResult = imgUploadCommonService.uploadTC(KEY, file);
        String signedUrl = imgUploadCommonService.uploadToOss(file, String.valueOf(user.getId()));
        /**
         * promptId 由java自己生成，与前端进行交换，调用python为markId
         */
//        String markId = UUID.randomUUID().toString();
        LocalRedrawPromptParams localRedrawParams = new LocalRedrawPromptParams();
        localRedrawParams.setImg_url(genGenericPara.getGenLocalRedrawPara().getImg_url());
        localRedrawParams.setMask_img_url(signedUrl);
        localRedrawParams.setPrompt(genGenericPara.getPrompt());
        localRedrawParams.setMark_id(markId);

        //非英文则翻译成英文进行生图
//    String englishPrompt = "";
//    if(genGenericPara.getTranslationFlag()){
//      englishPrompt = translationToEnglish(localRedrawParams.getPrompt());
//      localRedrawParams.setPrompt(englishPrompt);
//    }

        List<String> modelIdList = new ArrayList<>();
        modelIdList.add(realisticModelId);
        modelIdList.add(animeModelId);
        modelIdList.add(lineartModelId);
        modelIdList.add(ponyModelId);
        modelIdList.add(fluxdevModelId);
        /**
         * 线稿使用动漫模型，没有模型的图片，则通用模型
         */
        if (!modelIdList.contains(genGenericPara.getModel_id())) {
            localRedrawParams.setModel_id(realisticModelId);
        } else if (lineartModelId.equals(genGenericPara.getModel_id())) {
            localRedrawParams.setModel_id(animeModelId);
        } else {
            localRedrawParams.setModel_id(genGenericPara.getModel_id());
        }
        localRedrawParams.setCallback_url(callback_url);

        //将重绘的图片上传到腾讯云的url，存到接口入参
        GenLocalRedrawPara genLocalRedrawPara = new GenLocalRedrawPara();
        genLocalRedrawPara.setImg_url(genGenericPara.getGenLocalRedrawPara().getImg_url());
        genLocalRedrawPara.setMask_img_url(signedUrl);
        genLocalRedrawPara.setDenoise(genGenericPara.getGenLocalRedrawPara().getDenoise());
        genGenericPara.setGenLocalRedrawPara(genLocalRedrawPara);
        //数据入库
        PromptRecord promptRecord = new PromptRecord();
        promptRecord.setLoginName(user.getLoginName());
        promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genGenericPara));
        promptRecord.setPrompt(genGenericPara.getPrompt());
        promptRecord.setNegativePrompt("");
        promptRecord.setCreateBy(user.getLoginName());
        promptRecord.setCreateTime(LocalDateTime.now());
        promptRecord.setOriginCreate(OriginCreate.localRedraw.getValue());
        promptRecord.setBatchSize(1);
        promptRecord.setModelId(genGenericPara.getModel_id());
        promptRecord.setAspectRatio(genGenericPara.getResolution().getWidth() + " * " + genGenericPara.getResolution().getHeight());
        promptRecord.setMarkId(markId);
//    if(StringUtil.isNotBlank(englishPrompt)) {
//      promptRecord.setEnglishPrompt(JsonUtils.writeToJsonNode(englishPrompt));
//    }
        promptRecord.setPromptParams(JsonUtils.writeToJsonNode(localRedrawParams));
        promptRecord.setFeatureName(FeaturesType.inpaint.getValue());
        //Boolean notFastHour = loadBalanceService.judgeTheFastHourExceede(user.getLoginName());
        //判断是否属于fastHour内的任务
        promptRecord.setFastHour(fastHour);
        promptRecord.setPlatform(platform);
        promptRecordMapper.insert(promptRecord);

        //删除不需要的图片
        file.delete();

        //记录用户生图状态到redis
//        redisService.putDataToHash(user.getLoginName(), markId, -1, 2, TimeUnit.HOURS);
        redisService.stringSet(markId, user.getLoginName(), 2, TimeUnit.HOURS);

        /**
         * relax任务，满足一定生图量，则进入relax等待队列
         */
        String pushQueueResult = "";
        if ((relaxWaitTime > 0) && !fastHour) {
            //推送等待队列
            pushQueueResult = loadBalanceService.pushToRelaxWaitQueue(localRedrawParams.getModel_id(), "inpaint", markId, relaxWaitTime);
        } else {
            //推送到队列里
            pushQueueResult = loadBalanceService.pushToTaskQueue(localRedrawParams.getModel_id(), "inpaint", fastHour, markId, user);
        }

        /**
         * 判断进入黑名单是否有误
         */
        judgePushQueueError(pushQueueResult, markId, user.getLoginName());

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("markId", markId);
        resultMap.put("index", -1);
        resultMap.put("fastHour", fastHour);
        resultMap.put("featureName", promptRecord.getFeatureName());
        return resultMap;
    }

    public Map<String, Object> enlargeImage(GenGenericPara genParameters, CreatePicParams createPicParams, User user) throws IOException {
        Boolean fastHour = createPicParams.getFastHour();
        String platform = createPicParams.getPlatform();
        String markId = createPicParams.getMarkId();
        int relaxWaitTime = createPicParams.getRelaxWaitTime();

//        String markId = UUID.randomUUID().toString();
        EnlargeImageParams enlargeImageParams = new EnlargeImageParams();
        enlargeImageParams.setImg_url(genParameters.getEnlargeImagePara().getImg_url());
        enlargeImageParams.setDraw_img_url(genParameters.getEnlargeImagePara().getDraw_img_url());
        enlargeImageParams.setLeft(genParameters.getEnlargeImagePara().getLeft());
        enlargeImageParams.setTop(genParameters.getEnlargeImagePara().getTop());
        enlargeImageParams.setBottom(genParameters.getEnlargeImagePara().getBottom());
        enlargeImageParams.setRight(genParameters.getEnlargeImagePara().getRight());
        enlargeImageParams.setPrompt(genParameters.getPrompt());
        enlargeImageParams.setMark_id(markId);
        //非英文则翻译成英文进行生图
//    String englishPrompt = "";
////    if(genParameters.getTranslationFlag()){
////      englishPrompt = translationToEnglish(genParameters.getPrompt());
////      genParameters.setPrompt(englishPrompt);
////    }

        List<String> modelIdList = new ArrayList<>();
        modelIdList.add(realisticModelId);
        modelIdList.add(animeModelId);
        modelIdList.add(lineartModelId);
        modelIdList.add(ponyModelId);
        modelIdList.add(fluxdevModelId);
        /**
         * 线稿和没有模型的图片则通用模型
         */
        if (!modelIdList.contains(genParameters.getModel_id()) || lineartModelId.equals(genParameters.getModel_id())) {
            enlargeImageParams.setModel_id(realisticModelId);
        } else {
            enlargeImageParams.setModel_id(genParameters.getModel_id());
        }
        enlargeImageParams.setCallback_url(callback_url);

        //数据入库
        PromptRecord promptRecord = new PromptRecord();
        promptRecord.setLoginName(user.getLoginName());
        promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genParameters));
        promptRecord.setPrompt(genParameters.getPrompt());
        promptRecord.setNegativePrompt("");
        promptRecord.setCreateBy(user.getLoginName());
        promptRecord.setCreateTime(LocalDateTime.now());
        promptRecord.setOriginCreate(OriginCreate.enlargeImage.getValue());
        promptRecord.setBatchSize(1);
        promptRecord.setModelId(genParameters.getModel_id());
        promptRecord.setAspectRatio(genParameters.getResolution().getWidth() + " * " + genParameters.getResolution().getHeight());
        promptRecord.setMarkId(markId);
//    if(StringUtil.isNotBlank(englishPrompt)) {
//      promptRecord.setEnglishPrompt(JsonUtils.writeToJsonNode(englishPrompt));
//    }
        promptRecord.setPromptParams(JsonUtils.writeToJsonNode(enlargeImageParams));
        promptRecord.setFeatureName(FeaturesType.expand.getValue());
        //Boolean notFastHour = loadBalanceService.judgeTheFastHourExceede(user.getLoginName());
        //判断是否属于fastHour内的任务
        promptRecord.setFastHour(fastHour);
        promptRecord.setPlatform(platform);
        promptRecordMapper.insert(promptRecord);

        //记录用户生图状态到redis
//        redisService.putDataToHash(user.getLoginName(), markId, -1, 2, TimeUnit.HOURS);
        redisService.stringSet(markId, user.getLoginName(), 2, TimeUnit.HOURS);

        /**
         * relax任务，满足一定生图量，则进入relax等待队列
         */
        String pushQueueResult = "";
        if ((relaxWaitTime > 0) && !fastHour) {
            //推送等待队列
            pushQueueResult = loadBalanceService.pushToRelaxWaitQueue(enlargeImageParams.getModel_id(), "expand", markId, relaxWaitTime);
        } else {
            //推送到队列里
            pushQueueResult = loadBalanceService.pushToTaskQueue(enlargeImageParams.getModel_id(), "expand", fastHour, markId, user);
        }


        /**
         * 判断进入黑名单是否有误
         */
        judgePushQueueError(pushQueueResult, markId, user.getLoginName());

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("markId", markId);
        resultMap.put("index", -1);
        resultMap.put("fastHour", fastHour);
        resultMap.put("featureName", promptRecord.getFeatureName());
        return resultMap;
    }

    public Map<String, Object> lineRecolor(GenGenericPara genParameters, CreatePicParams createPicParams, User user) throws IOException {
        Boolean fastHour = createPicParams.getFastHour();
        String platform = createPicParams.getPlatform();
        String markId = createPicParams.getMarkId();
        int relaxWaitTime = createPicParams.getRelaxWaitTime();

//        String markId = UUID.randomUUID().toString();
        LineRecolorParams lineRecolorParams = new LineRecolorParams();
        lineRecolorParams.setImg_url(genParameters.getLineRecolorPara().getImg_url());
        lineRecolorParams.setPrompt(genParameters.getPrompt());
        lineRecolorParams.setMark_id(markId);
        lineRecolorParams.setCallback_url(callback_url);

        String promptId = genParameters.getPromptId();
        if (StringUtil.isNotBlank(promptId)) {
            //查询原始图片数据
            LambdaQueryWrapper<PromptRecord> qw = new LambdaQueryWrapper<PromptRecord>();
            qw.eq(PromptRecord::getPromptId, promptId);
            qw.eq(PromptRecord::getLoginName, user.getLoginName());
            PromptRecord originPromptRecord = promptRecordMapper.selectOne(qw);

            if (Objects.isNull(originPromptRecord)) {
                log.error("查询不到原始图片的相关信息，图片promptId：{}，用户名称：{}", promptId, user.getLoginName());
                throw new OperationNotAllowedException("lineRecolor failed !");
            }

            if (OriginCreate.customUpload.getValue().equals(originPromptRecord.getOriginCreate())) {
                genParameters.setModel_id(animeModelId);
            }
        }

        /**
         * 线稿和动漫则用动漫,pony用自身，其他使用真实
         */
        if (animeModelId.equals(genParameters.getModel_id()) || lineartModelId.equals(genParameters.getModel_id())) {
            lineRecolorParams.setModel_id(animeModelId);
        } else if (ponyModelId.equals(genParameters.getModel_id())) {
            lineRecolorParams.setModel_id(ponyModelId);
        } else {
            lineRecolorParams.setModel_id(realisticModelId);
        }

        //数据入库
        PromptRecord promptRecord = new PromptRecord();
        promptRecord.setLoginName(user.getLoginName());
        promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genParameters));
        promptRecord.setPrompt(genParameters.getPrompt());
        promptRecord.setNegativePrompt("");
        promptRecord.setCreateBy(user.getLoginName());
        promptRecord.setCreateTime(LocalDateTime.now());
        promptRecord.setOriginCreate(OriginCreate.lineRecolor.getValue());
        promptRecord.setBatchSize(1);
        promptRecord.setModelId(genParameters.getModel_id());
        promptRecord.setAspectRatio(genParameters.getResolution().getWidth() + " * " + genParameters.getResolution().getHeight());
        promptRecord.setMarkId(markId);
        promptRecord.setPromptParams(JsonUtils.writeToJsonNode(lineRecolorParams));
        promptRecord.setFeatureName(FeaturesType.lineRecolor.getValue());
        //Boolean notFastHour = loadBalanceService.judgeTheFastHourExceede(user.getLoginName());
        //判断是否属于fastHour内的任务
        promptRecord.setFastHour(fastHour);
        promptRecord.setPlatform(platform);
        promptRecordMapper.insert(promptRecord);

        //记录用户生图状态到redis
//        redisService.putDataToHash(user.getLoginName(), markId, -1, 2, TimeUnit.HOURS);
        redisService.stringSet(markId, user.getLoginName(), 2, TimeUnit.HOURS);

        /**
         * relax任务，满足一定生图量，则进入relax等待队列
         */
        String pushQueueResult = "";
        if ((relaxWaitTime > 0) && !fastHour) {
            //推送等待队列
            pushQueueResult = loadBalanceService.pushToRelaxWaitQueue(genParameters.getModel_id(), "lineRecolor", markId, relaxWaitTime);
        } else {
            //推送到队列里
            pushQueueResult = loadBalanceService.pushToTaskQueue(genParameters.getModel_id(), "lineRecolor", fastHour, markId, user);
        }

        /**
         * 判断进入黑名单是否有误
         */
        judgePushQueueError(pushQueueResult, markId, user.getLoginName());

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("markId", markId);
        resultMap.put("index", -1);
        resultMap.put("fastHour", fastHour);
        resultMap.put("featureName", promptRecord.getFeatureName());
        return resultMap;
    }


//    public Map<String, Object> varyImage(GenGenericPara genParameters, Boolean specialFlag,String platform, User user) throws JsonProcessingException {
//
//        String markId = UUID.randomUUID().toString();
//        VaryParams varyParams = new VaryParams();
//        varyParams.setImg_url(genParameters.getVaryPara().getImg_url());
//        varyParams.setPrompt(StringUtil.isNotBlank(genParameters.getPrompt()) ? genParameters.getPrompt() : "");
//        varyParams.setNegative_prompt(StringUtil.isNotBlank(genParameters.getNegative_prompt()) ? genParameters.getNegative_prompt() : "");
//        varyParams.setMark_id(markId);
//        varyParams.setCallback_url(callback_url);
//        varyParams.setStrength(genParameters.getVaryPara().getStrength());
//        varyParams.setBatch_size(genParameters.getResolution().getBatch_size());
//        varyParams.setModel_id(genParameters.getModel_id());
//        //对于非图生图和文生图的操作，需要反推提示词
//        String originCreate = genParameters.getVaryPara().getOriginCreate();
//        if (StringUtil.isNotBlank(originCreate) && (OriginCreate.picCreate.getValue().equals(originCreate) || OriginCreate.create.getValue().equals(originCreate))) {
//            varyParams.setFlag(" ");
//        } else {
//            varyParams.setFlag("img_vary_wd");
//        }
//
//        //数据入库
//        PromptRecord promptRecord = new PromptRecord();
//        promptRecord.setLoginName(user.getLoginName());
//        promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genParameters));
//        promptRecord.setPrompt(genParameters.getPrompt());
//        promptRecord.setNegativePrompt(genParameters.getNegative_prompt());
//        promptRecord.setCreateBy(user.getLoginName());
//        promptRecord.setCreateTime(LocalDateTime.now());
//        promptRecord.setOriginCreate(OriginCreate.vary.getValue());
//        promptRecord.setBatchSize(genParameters.getResolution().getBatch_size());
//        promptRecord.setModelId(genParameters.getModel_id());
//        promptRecord.setAspectRatio(genParameters.getResolution().getWidth() + " * " + genParameters.getResolution().getHeight());
//        promptRecord.setMarkId(markId);
//        promptRecord.setPromptParams(JsonUtils.writeToJsonNode(varyParams));
//        promptRecord.setFeatureName(FeaturesType.vary.getValue());
//        //Boolean notFastHour = loadBalanceService.judgeTheFastHourExceede(user.getLoginName());
//        //判断是否属于fastHour内的任务
//        promptRecord.setFastHour(specialFlag);
//        promptRecord.setPlatform(platform);
//        promptRecordMapper.insert(promptRecord);
//
//        ActionLog actionLog = new ActionLog();
//        actionLog.setAction("vary");
//        actionLog.setCreateTime(LocalDateTime.now());
//        actionLog.setCreateBy(user.getLoginName());
//        actionLog.setUserId(user.getId());
//        actionLog.setUserLoginName(user.getLoginName());
//        actionLog.setContent("vary");
//        actionLogMapper.insert(actionLog);
//
//        //记录用户生图状态到redis
//        redisService.putDataToHash(user.getLoginName(), markId, -1, 2, TimeUnit.HOURS);
//        redisService.stringSet(markId, user.getLoginName(), 2, TimeUnit.HOURS);
//
//        Long queueSize = 0L;
//        //推送到队列里
//        String pushQueueResult = loadBalanceService.pushToTaskQueue(genParameters.getModel_id(), "vary", specialFlag, markId, user.getBlackListFlag());
//
//        /**
//         * 判断进入黑名单是否有误
//         */
//        judgePushQueueError(pushQueueResult, markId, user.getLoginName());
//
//        Map<String, Object> resultMap = new HashMap<>();
//        resultMap.put("markId", markId);
//        resultMap.put("index", -1);
//        resultMap.put("fastHour", specialFlag);
//        return resultMap;
//    }


    // 新增的写入元数据方法
    public void writeImageMetadata(File file, String parameters) throws IOException {
        ImageInputStream input = null;
        ImageOutputStream output = null;

        try {
            input = ImageIO.createImageInputStream(file);
            output = ImageIO.createImageOutputStream(file);

            ImageReader reader = getImageReader(input);
            if (reader == null) {
                log.error("未找到合适的 ImageReader");
                return;
            }

            reader.setInput(input);
            IIOImage image = reader.readAll(0, null);

            // 删除所有的parameters元数据
            removeTextEntry(image.getMetadata(), "parameters");

            // 添加新的parameters元数据
            addTextEntry(image.getMetadata(), "parameters", parameters);

            ImageWriter writer = ImageIO.getImageWriter(reader);
            writer.setOutput(output);
            writer.write(image);

            // 可选：读取元数据以验证
//      readMetadata(file, "foo");

        } catch (Exception e) {
            log.error("发生意外错误", e);
        } finally {
            input.close();
            output.close();
        }
    }

    private static ImageReader getImageReader(ImageInputStream input) {
        Iterator<ImageReader> readers = ImageIO.getImageReaders(input);
        return readers.hasNext() ? readers.next() : null;
    }

    private static void addTextEntry(final IIOMetadata metadata, final String key, final String value) throws IIOInvalidTreeException {
        IIOMetadataNode root = (IIOMetadataNode) metadata.getAsTree(IIOMetadataFormatImpl.standardMetadataFormatName);
        IIOMetadataNode text = new IIOMetadataNode("Text");

        IIOMetadataNode textEntry = new IIOMetadataNode("TextEntry");
        textEntry.setAttribute("keyword", key);
        textEntry.setAttribute("value", value);
        text.appendChild(textEntry);

        root.appendChild(text);
        metadata.mergeTree(IIOMetadataFormatImpl.standardMetadataFormatName, root);
    }

    private static String readMetadata(File file, String key) throws IOException {
        try (ImageInputStream input = ImageIO.createImageInputStream(file)) {
            ImageReader reader = getImageReader(input);
            if (reader != null) {
                reader.setInput(input);
                return getTextEntry(reader.getImageMetadata(0), key);
            }
        }
        return null;
    }

    private static String getTextEntry(final IIOMetadata metadata, final String key) {
        IIOMetadataNode root = (IIOMetadataNode) metadata.getAsTree(IIOMetadataFormatImpl.standardMetadataFormatName);
        NodeList entries = root.getElementsByTagName("TextEntry");

        for (int i = 0; i < entries.getLength(); i++) {
            IIOMetadataNode node = (IIOMetadataNode) entries.item(i);
            if (key.equals(node.getAttribute("keyword"))) {
                return node.getAttribute("value");
            }
        }
        return null;
    }

    private static void removeTextEntry(final IIOMetadata metadata, final String key) throws IIOInvalidTreeException {
        IIOMetadataNode root = (IIOMetadataNode) metadata.getAsTree(IIOMetadataFormatImpl.standardMetadataFormatName);

        // 获取 Text 节点
        IIOMetadataNode text = null;
        for (int i = 0; i < root.getLength(); i++) {
            IIOMetadataNode child = (IIOMetadataNode) root.item(i);
            if ("Text".equals(child.getNodeName())) {
                text = child;
                break;
            }
        }

        // 如果 Text 节点不存在，直接返回
        if (text == null) {
            return;
        }

        // 遍历 Text 节点下的子节点，查找匹配的 TextEntry
        for (int i = 0; i < text.getLength(); i++) {
            IIOMetadataNode textEntry = (IIOMetadataNode) text.item(i);
            if ("TextEntry".equals(textEntry.getNodeName()) && key.equals(textEntry.getAttribute("keyword"))) {
                text.removeChild(textEntry);
            }
        }
        metadata.setFromTree(IIOMetadataFormatImpl.standardMetadataFormatName, root);

    }


    public Map<String, Object> inpaint(GenGenericPara genGenericPara, CreatePicParams createPicParams, User user) throws JsonProcessingException {
        Boolean fastHour = createPicParams.getFastHour();
        String platform = createPicParams.getPlatform();
        String markId = createPicParams.getMarkId();
        int relaxWaitTime = createPicParams.getRelaxWaitTime();

        LocalRedrawPromptParams localRedrawParams = new LocalRedrawPromptParams();
        localRedrawParams.setImg_url(genGenericPara.getGenLocalRedrawPara().getImg_url());
        localRedrawParams.setMask_img_url(genGenericPara.getGenLocalRedrawPara().getMask_img_url());
        localRedrawParams.setPrompt(genGenericPara.getPrompt());
        localRedrawParams.setMark_id(markId);

        //非英文则翻译成英文进行生图
//    String englishPrompt = "";
//    if(genGenericPara.getTranslationFlag()){
//      englishPrompt = translationToEnglish(localRedrawParams.getPrompt());
//      localRedrawParams.setPrompt(englishPrompt);
//    }

        List<String> modelIdList = new ArrayList<>();
        modelIdList.add(realisticModelId);
        modelIdList.add(animeModelId);
        modelIdList.add(lineartModelId);
        modelIdList.add(ponyModelId);
        /**
         * 线稿使用动漫模型，没有模型的图片，则通用模型
         */
        if (!modelIdList.contains(genGenericPara.getModel_id())) {
            localRedrawParams.setModel_id(realisticModelId);
        } else if (lineartModelId.equals(genGenericPara.getModel_id())) {
            localRedrawParams.setModel_id(animeModelId);
        } else {
            localRedrawParams.setModel_id(genGenericPara.getModel_id());
        }
        localRedrawParams.setCallback_url(callback_url);

        //将重绘的图片上传到腾讯云的url，存到接口入参
        GenLocalRedrawPara genLocalRedrawPara = new GenLocalRedrawPara();
        genLocalRedrawPara.setImg_url(genGenericPara.getGenLocalRedrawPara().getImg_url());
        genLocalRedrawPara.setMask_img_url(genGenericPara.getGenLocalRedrawPara().getMask_img_url());
        genLocalRedrawPara.setDraw_img_url(genGenericPara.getGenLocalRedrawPara().getDraw_img_url());
        genLocalRedrawPara.setDenoise(genGenericPara.getGenLocalRedrawPara().getDenoise());
        genGenericPara.setGenLocalRedrawPara(genLocalRedrawPara);
        //数据入库
        PromptRecord promptRecord = new PromptRecord();
        promptRecord.setLoginName(user.getLoginName());
        promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genGenericPara));
        promptRecord.setPrompt(genGenericPara.getPrompt());
        promptRecord.setNegativePrompt("");
        promptRecord.setCreateBy(user.getLoginName());
        promptRecord.setCreateTime(LocalDateTime.now());
        promptRecord.setOriginCreate(OriginCreate.localRedraw.getValue());
        promptRecord.setBatchSize(1);
        promptRecord.setModelId(genGenericPara.getModel_id());
        promptRecord.setAspectRatio(genGenericPara.getResolution().getWidth() + " * " + genGenericPara.getResolution().getHeight());
        promptRecord.setMarkId(markId);
//    if(StringUtil.isNotBlank(englishPrompt)) {
//      promptRecord.setEnglishPrompt(JsonUtils.writeToJsonNode(englishPrompt));
//    }
        promptRecord.setPromptParams(JsonUtils.writeToJsonNode(localRedrawParams));
        promptRecord.setFeatureName(FeaturesType.inpaint.getValue());
        //Boolean notFastHour = loadBalanceService.judgeTheFastHourExceede(user.getLoginName());
        //判断是否属于fastHour内的任务
        promptRecord.setFastHour(fastHour);
        promptRecord.setPlatform(platform);
        promptRecordMapper.insert(promptRecord);

        //记录用户生图状态到redis
//        redisService.putDataToHash(user.getLoginName(), markId, -1, 2, TimeUnit.HOURS);
        redisService.stringSet(markId, user.getLoginName(), 2, TimeUnit.HOURS);

        /**
         * relax任务，满足一定生图量，则进入relax等待队列
         */
        String pushQueueResult = "";
        if ((relaxWaitTime > 0) && !fastHour) {
            //推送等待队列
            pushQueueResult = loadBalanceService.pushToRelaxWaitQueue(localRedrawParams.getModel_id(), "inpaint", markId, relaxWaitTime);
        } else {
            //推送到队列里
            pushQueueResult = loadBalanceService.pushToTaskQueue(localRedrawParams.getModel_id(), "inpaint", fastHour, markId, user);
        }

        /**
         * 判断进入黑名单是否有误
         */
        judgePushQueueError(pushQueueResult, markId, user.getLoginName());

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("markId", markId);
        resultMap.put("index", -1);
        resultMap.put("fastHour", fastHour);
        resultMap.put("featureName", promptRecord.getFeatureName());
        return resultMap;
    }

    public List<ModelInformation.ModelAbout> buildModelList(List<GptModelAbout> modelAboutList, String platform) {

        List<ModelInformation.ModelAbout> modelAboutDtos = new ArrayList<>();

        modelAboutList.stream()
                .filter(modelAbout -> {
                    String plat = modelAbout.getPlatform();
                    return plat != null && Arrays.asList(plat.split(",")).contains(platform);
                })
                .collect(Collectors.toList());

        for (GptModelAbout gptModelAbout : modelAboutList) {
            ModelInformation.ModelAbout modelAboutDto = new ModelInformation.ModelAbout();
            BeanUtils.copyProperties(gptModelAbout, modelAboutDto);
            if (com.lx.pl.util.StringUtils.isNotBlank(gptModelAbout.getDefaultConfig())) {
                try {
                    modelAboutDto.setDefaultConfig(JsonUtils.fromString(gptModelAbout.getDefaultConfig(), ModelInformation.DefaultConfig.class));
                } catch (Exception e) {
                    log.error("模型列表转换失败", e);
                    modelAboutDto.setDefaultConfig(ModelInformation.DefaultConfig.builder().width(1024).height(1024).build());
                }
            } else {
                modelAboutDto.setDefaultConfig(ModelInformation.DefaultConfig.builder().width(1024).height(1024).build());
            }
            if (com.lx.pl.util.StringUtils.isNotBlank(gptModelAbout.getSupportStyleList())) {
                try {
                    modelAboutDto.setSupportStyleList(JsonUtils.writeToList(gptModelAbout.getSupportStyleList(), ModelInformation.SupportStyle.class));
                } catch (Exception e) {
                    log.error("模型列表转换失败", e);
                    modelAboutDto.setSupportStyleList(new ArrayList<>());
                }
            } else {
                modelAboutDto.setSupportStyleList(new ArrayList<>());
            }
            modelAboutDtos.add(modelAboutDto);
        }
        return modelAboutDtos;
    }
}
