package com.lx.pl.client;

import com.lx.pl.openai.dto.images.OpenAiImagesRequest;
import com.lx.pl.openai.dto.images.OpenAiImagesResponse;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;

/**
 * GPT Image API客户端
 *
 * <AUTHOR>
 */
public interface GptImageApiClient {

    /**
     * 生成图像
     *
     * @param authorization 授权头
     * @param request       请求参数
     * @return 图像生成响应
     */
    @POST("/v1/images/generations")
    Call<OpenAiImagesResponse> generateImages(
            @Header("Authorization") String authorization,
            @Body OpenAiImagesRequest request
    );
}
