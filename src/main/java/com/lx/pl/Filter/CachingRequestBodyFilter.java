package com.lx.pl.Filter;

import com.lx.pl.util.CachedBodyHttpServletRequest;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/7/1
 * @description
 */
public class CachingRequestBodyFilter extends OncePerRequestFilter {
    private static final int MAX_BODY_SIZE = 2 * 1024 * 1024;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (shouldCacheBody(request)) {
            CachedBodyHttpServletRequest wrappedRequest =
                    new CachedBodyHttpServletRequest(request);

            filterChain.doFilter(wrappedRequest, response);
        } else {
            filterChain.doFilter(request, response);
        }
    }

    private boolean shouldCacheBody(HttpServletRequest request) {
        boolean shouldCache = false;
        try {
            String method = request.getMethod();
            String contentType = request.getContentType();
            int contentLength = request.getContentLength();

            // 检查基本条件
            boolean validMethod = Set.of("POST", "PUT", "PATCH").contains(method);
            boolean validContentType = contentType != null &&
                    !contentType.startsWith("multipart/");
            boolean validSize = contentLength > 0 && contentLength <= MAX_BODY_SIZE;
            shouldCache = validMethod && validContentType && validSize;
        } catch (Exception e) {
            logger.error("Error occurred while checking request body", e);
        }

        return shouldCache;
    }
}
