package com.lx.pl.pay.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.apple.domain.PayApplePurchaseRecord;
import com.lx.pl.pay.common.domain.PayLumenRecord;
import com.lx.pl.pay.paypal.model.domain.PayPalLogicSubscription;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentRecord;
import com.lx.pl.pay.paypal.model.domain.PayPalProduct;
import com.lx.pl.pay.stripe.domain.PayLogicPurchaseRecord;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Lumen 记录服务接口
 */
public interface PayLumenRecordService extends IService<PayLumenRecord> {

    void saveLumenRecord(PayLogicPurchaseRecord payLogicPurchaseRecord);

    void recallLumen(Long payLogicPurchaseRecordId, String message);

    void saveOneTimeLumenRecord(List<PayLogicPurchaseRecord> payLogicPurchaseRecordList);

    void saveLumenRecordForApple(PayApplePurchaseRecord record);

    void saveOneTimeLumenRecordForApple(PayApplePurchaseRecord record);

    void restoreLumenByUserId(String originalTransactionId, Long newUserId, String loginName);

    void saveLumenRecordForPayPal(PayPalLogicSubscription payPalLogicSubscription, PayPalProduct paypalPlan);

    @Transactional(rollbackFor = Exception.class)
    void recallLumenForPaypal(Long orderId, String message);

    void saveOneTimeLumenForPaypal(List<PayPalOrderPaymentRecord> existRecords);

    List<PayLumenRecord> queryByLogicId(Long logicId);

    void giftLumen(String loginName, Integer lumen);

    void invalidatedSubscriptionForPaypal(String srcSubscriptionId, Long id);

    void reActivedSubscriptionForPaypal(String subscriptionId, Long palLogicSubscriptionId);

    boolean hasPurchasedLumen(Long userId);

    // 可以添加一些自定义方法，例如：
    // List<PayLumenRecord> findByUserId(Long userId);
}
