package com.lx.pl.pay.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.enums.LumenType;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.apple.domain.PayApplePurchaseRecord;
import com.lx.pl.pay.common.domain.PayLumenRecord;
import com.lx.pl.pay.common.mapper.PayLumenRecordMapper;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.paypal.model.domain.PayPalLogicSubscription;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentItem;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentRecord;
import com.lx.pl.pay.paypal.model.domain.PayPalProduct;
import com.lx.pl.pay.paypal.service.PayPalOrderPaymentItemService;
import com.lx.pl.pay.paypal.service.PayPalProductService;
import com.lx.pl.pay.stripe.domain.PayLogicPurchaseRecord;
import com.lx.pl.service.RedisService;
import com.lx.pl.service.UserService;
import com.lx.pl.service.VipService;
import com.lx.pl.util.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.lx.pl.pay.apple.VipPlatform.PAYPAL;

/**
 * Lumen 记录服务实现类
 *
 * <AUTHOR>
 */
@Service
public class PayLumenRecordServiceImpl extends ServiceImpl<PayLumenRecordMapper, PayLumenRecord> implements PayLumenRecordService, ApplicationContextAware {

    static Logger log = LoggerFactory.getLogger("stripe-pay-msg");
    static Logger logApple = LoggerFactory.getLogger("apple-pay-msg");
    static Logger logPaypal = LoggerFactory.getLogger("paypal-pay-msg");

    @Autowired
    RedisService redisService;

    private ApplicationContext applicationContext;

    @Autowired
    private PayPalProductService productService;
    @Autowired
    private PayPalOrderPaymentItemService payPalOrderPaymentItemService;


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Lazy
    private VipService vipService;
    @Autowired
    private SubscriptionCurrentService subscriptionCurrentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLumenRecord(PayLogicPurchaseRecord payLogicPurchaseRecord) {
        List<PayLumenRecord> payLogicPurchaseRecords = generatePeriods(payLogicPurchaseRecord);
        if (payLogicPurchaseRecords != null && !payLogicPurchaseRecords.isEmpty()) {
            log.info("saveLumenRecord: {}", payLogicPurchaseRecords);
            this.saveBatch(payLogicPurchaseRecords);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recallLumen(Long payLogicPurchaseRecordId, String message) {
        log.info("recallLumen: {}", payLogicPurchaseRecordId);
        boolean update = this.lambdaUpdate().eq(PayLumenRecord::getPayLogicPurchaseRecordId, payLogicPurchaseRecordId)
                .set(PayLumenRecord::getInvalid, true).set(PayLumenRecord::getInvalidMessage, message)
                .set(PayLumenRecord::getUpdateTime, LocalDateTime.now()).update();
        log.info("end recallLumen: {} {}", payLogicPurchaseRecordId, update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOneTimeLumenRecord(List<PayLogicPurchaseRecord> payLogicPurchaseRecordList) {
        log.info("start saveOneTimeLumen for {} ", payLogicPurchaseRecordList.size());
        List<PayLumenRecord> payLumenRecords = new ArrayList<>();
        for (PayLogicPurchaseRecord payLogicPurchaseRecord : payLogicPurchaseRecordList) {
            log.info("start saveOneTimeLumenRecord: {}", payLogicPurchaseRecord);
            PayLumenRecord record = new PayLumenRecord();
            record.setPayLogicPurchaseRecordId(payLogicPurchaseRecord.getId());
            record.setUserId(payLogicPurchaseRecord.getUserId());
            record.setLoginName(payLogicPurchaseRecord.getLoginName());
            record.setCustomerId(payLogicPurchaseRecord.getCustomerId());
            record.setType(LumenType.recharge.getValue());
            ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
            record.setCurrentPeriodStart(now.toEpochSecond());
            // end day 为
            ZonedDateTime zonedDateTime = now.plusYears(1);
            ZonedDateTime endOfDay = zonedDateTime.toLocalDate().atTime(23, 59, 59, 999999999).atZone(now.getZone());
            record.setCurrentPeriodEnd(endOfDay.toEpochSecond());
            record.setVipPlatForm(payLogicPurchaseRecord.getVipPlatForm());
            record.setLogicPeriodStart(record.getCurrentPeriodStart());
            record.setLogicPeriodEnd(record.getCurrentPeriodEnd());

            record.setLumenQty(payLogicPurchaseRecord.getLumenQty() * payLogicPurchaseRecord.getCount());
            record.setLumenLeftQty(payLogicPurchaseRecord.getLumenQty() * payLogicPurchaseRecord.getCount());
            record.setMixed(false);
            record.setInvalid(false);
            record.setCreateTime(LocalDateTime.now());
            payLumenRecords.add(record);
        }
        this.saveBatch(payLumenRecords);
        log.info("end saveOneTimeLumenRecord, total save {} price", payLumenRecords.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLumenRecordForApple(PayApplePurchaseRecord record) {
        logApple.info("start saveLumenRecordForApple {} {}", record.getUserId(), record.getTransactionId());
        List<PayLumenRecord> payLumenRecords = generatePeriodsApple(record);
        if (!payLumenRecords.isEmpty()) {
            this.saveBatch(payLumenRecords);
        }
        logApple.info("end saveLumenRecordForApple: {}", payLumenRecords);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOneTimeLumenRecordForApple(PayApplePurchaseRecord record) {
        PayLumenRecord saveRecord = new PayLumenRecord();
        saveRecord.setPayLogicPurchaseRecordId(record.getId());

        saveRecord.setUserId(record.getUserId());
        saveRecord.setLoginName(record.getLoginName());
        saveRecord.setType(LumenType.recharge.getValue());
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
        ZonedDateTime zonedDateTime = now.plusYears(1);
        ZonedDateTime endOfDay = zonedDateTime.toLocalDate().atTime(23, 59, 59, 999999999).atZone(now.getZone());
        saveRecord.setCurrentPeriodStart(record.getPurchaseDate());
        saveRecord.setCurrentPeriodEnd(endOfDay.toEpochSecond());
        saveRecord.setLogicPeriodStart(now.toEpochSecond());
        saveRecord.setLogicPeriodEnd(endOfDay.toEpochSecond());

        saveRecord.setVipPlatForm(record.getVipPlatForm());

        record.setOriginalTransactionId(record.getOriginalTransactionId());
        record.setTransactionId(record.getTransactionId());

        saveRecord.setLumenQty(record.getLumenQty() * record.getCount());
        saveRecord.setInvalid(false);
        saveRecord.setLumenLeftQty(record.getLumenQty() * record.getCount());
        saveRecord.setMixed(false);
        saveRecord.setCreateTime(LocalDateTime.now());
        this.save(saveRecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLumenRecordForPayPal(PayPalLogicSubscription payPalLogicSubscription, PayPalProduct payPalPlan) {
        logPaypal.info("start saveLumenRecordForPayPal {} {} {}", payPalLogicSubscription.getUserId(), payPalLogicSubscription.getSubscriptionId(), payPalLogicSubscription.getSubStartSec());
        List<PayLumenRecord> payLumenRecords = generatePeriodsPaypal(payPalLogicSubscription, payPalPlan);
        if (!payLumenRecords.isEmpty()) {
            this.saveBatch(payLumenRecords);
            logPaypal.info("end saveLumenRecordForPayPal: {}", payLumenRecords);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void recallLumenForPaypal(Long orderId, String message) {
        log.info("recallLumen: {}", orderId);
        boolean update = this.lambdaUpdate().eq(PayLumenRecord::getOrderId, orderId)
                .set(PayLumenRecord::getInvalid, true).set(PayLumenRecord::getInvalidMessage, message)
                .set(PayLumenRecord::getUpdateTime, LocalDateTime.now()).update();
        log.info("end recallLumen: {} {}", orderId, update);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOneTimeLumenForPaypal(List<PayPalOrderPaymentRecord> existRecords) {


        User userById = applicationContext.getBean(UserService.class).getUserById(existRecords.get(0).getUserId());
        boolean hasPurchasedLumen = this.hasPurchasedLumen(userById.getId());
        List<PayLumenRecord> recordList = new ArrayList<>();
        for (PayPalOrderPaymentRecord existRecord1 : existRecords) {
            String orderId = existRecord1.getOrderId();
            if (!this.queryByOrderId(orderId).isEmpty()) {
                log.info("order already exists: {}", orderId);
                continue;
            }
            log.info("start saveOneTimeLumen {} {} {}", existRecord1.getUserId(), existRecord1.getLoginName(), existRecord1.getAmount());

            List<PayPalOrderPaymentItem> byPaymentId = payPalOrderPaymentItemService.findByOrderId(orderId);
            for (PayPalOrderPaymentItem payPalOrderPaymentItem : byPaymentId) {
                log.info("start saveOneTimeLumen {} {}", payPalOrderPaymentItem.getUserId(), payPalOrderPaymentItem.getProductId());
                PayLumenRecord record = new PayLumenRecord();
                record.setPayLogicPurchaseRecordId(payPalOrderPaymentItem.getId());
                record.setOrderId(orderId);

                record.setUserId(payPalOrderPaymentItem.getUserId());
                record.setLoginName(payPalOrderPaymentItem.getLoginName());
                record.setCustomerId(payPalOrderPaymentItem.getUserId().toString());
                record.setType(LumenType.recharge.getValue());
                ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
                record.setCurrentPeriodStart(now.toEpochSecond());
                // end day 为
                ZonedDateTime zonedDateTime = now.plusYears(1);
                ZonedDateTime endOfDay = zonedDateTime.toLocalDate().atTime(23, 59, 59, 999999999).atZone(now.getZone());
                record.setCurrentPeriodEnd(endOfDay.toEpochSecond());
                record.setVipPlatForm(PAYPAL.getPlatformName());
                record.setLogicPeriodStart(record.getCurrentPeriodStart());
                record.setLogicPeriodEnd(record.getCurrentPeriodEnd());
                String referenceId = payPalOrderPaymentItem.getProductId();
                PayPalProduct paypalProductByPlanId = productService.getPaypalProductByPlanId(referenceId);
                if (paypalProductByPlanId != null) {
                    record.setLumenQty(paypalProductByPlanId.getLumen() * ((payPalOrderPaymentItem.getQty() == null || payPalOrderPaymentItem.getQty() <= 0) ? 1 : payPalOrderPaymentItem.getQty()));
                } else {
                    record.setLumenQty(500);
                    log.info("paypalProductByPlanId is null");
                }
                record.setLumenLeftQty(record.getLumenQty());
                record.setMixed(false);
                record.setInvalid(false);
                record.setCreateTime(LocalDateTime.now());
                recordList.add(record);
                if (!hasPurchasedLumen && paypalProductByPlanId != null && paypalProductByPlanId.getInitialLumen() > 0) {
                    PayLumenRecord firstChargeRecord = new PayLumenRecord();
                    BeanUtils.copyProperties(record, firstChargeRecord);
                    firstChargeRecord.setLumenQty(paypalProductByPlanId.getInitialLumen());
                    firstChargeRecord.setLumenLeftQty(firstChargeRecord.getLumenQty());
                    firstChargeRecord.setType(LumenType.gift.getValue());
                    recordList.add(firstChargeRecord);
                }
                log.info("end saveOneTimeLumen {} {}", payPalOrderPaymentItem.getUserId(), payPalOrderPaymentItem.getProductId());
            }
        }
        this.saveBatch(recordList);
        log.info("end saveOneTimeLumenRecord: {}", recordList);
    }

    private List<PayLumenRecord> queryByOrderId(String orderId) {
        return this.lambdaQuery().eq(PayLumenRecord::getOrderId, orderId)
                .list();
    }

    @Override
    public List<PayLumenRecord> queryByLogicId(Long logicId) {
        return this.lambdaQuery().eq(PayLumenRecord::getPayLogicPurchaseRecordId, logicId)
                .eq(PayLumenRecord::getInvalid, false)
                .list();
    }

    private List<PayLumenRecord> generatePeriodsPaypal(PayPalLogicSubscription payPalLogicSubscription, PayPalProduct paypalPlan) {
        long periodStart = LocalDateTime.now().toEpochSecond(ZoneOffset.UTC);
        String priceInterval = paypalPlan.getPriceInterval();
        int monthsCount = 1;
        if ("year".equals(priceInterval)) {
            monthsCount = (12);
        }

        PayLumenRecord record = new PayLumenRecord();
        record.setCreateTime(LocalDateTime.now());
        record.setPayLogicPurchaseRecordId(payPalLogicSubscription.getId());
        record.setUserId(payPalLogicSubscription.getUserId());
        record.setLoginName(payPalLogicSubscription.getLoginName());
        record.setType(LumenType.vip.getValue());
        record.setLumenQty(paypalPlan.getLumen());
        record.setLumenLeftQty(paypalPlan.getLumen());
        record.setVipPlatForm(PAYPAL.getPlatformName());

        record.setMixed(false);
        record.setInvalid(false);
        record.setCreateTime(LocalDateTime.now());
        return getPayLumenRecords(record, periodStart, monthsCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restoreLumenByUserId(String originalTransactionId, Long newUserId, String loginName) {
        this.lambdaUpdate().eq(PayLumenRecord::getOriginalTransactionId, originalTransactionId)
                .eq(PayLumenRecord::getInvalid, false)
                .gt(PayLumenRecord::getLogicPeriodEnd, System.currentTimeMillis() / 1000 - 10)
                .set(PayLumenRecord::getLoginName, loginName).set(PayLumenRecord::getUserId, newUserId).update();
    }

    public static List<PayLumenRecord> generatePeriodsApple(PayApplePurchaseRecord payLogicPurchaseRecord) {
        long periodStart = payLogicPurchaseRecord.getPurchaseDate();
        Integer monthsCount = payLogicPurchaseRecord.getCount();
        PayLumenRecord record = new PayLumenRecord();
        record.setCreateTime(LocalDateTime.now());
        record.setPayLogicPurchaseRecordId(payLogicPurchaseRecord.getId());

        record.setUserId(payLogicPurchaseRecord.getUserId());
        record.setLoginName(payLogicPurchaseRecord.getLoginName());
        record.setOriginalTransactionId(payLogicPurchaseRecord.getOriginalTransactionId());
        record.setTransactionId(payLogicPurchaseRecord.getTransactionId());
        record.setType(LumenType.vip.getValue());
        record.setLumenQty(payLogicPurchaseRecord.getLumenQty());
        record.setLumenLeftQty(payLogicPurchaseRecord.getLumenQty());
        record.setVipPlatForm(VipPlatform.IOS.getPlatformName());
        record.setOriginalTransactionId(payLogicPurchaseRecord.getOriginalTransactionId());
        record.setTransactionId(payLogicPurchaseRecord.getTransactionId());

        record.setMixed(false);
        record.setInvalid(false);
        record.setCreateTime(LocalDateTime.now());
        return getPayLumenRecords(record, periodStart, monthsCount);
    }

    private static List<PayLumenRecord> generatePeriods(PayLogicPurchaseRecord payLogicPurchaseRecord) {
        long periodStart = payLogicPurchaseRecord.getCurrentPeriodStart();
        Integer monthsCount = payLogicPurchaseRecord.getCount();
        PayLumenRecord record = new PayLumenRecord();
        record.setCreateTime(LocalDateTime.now());
        record.setPayLogicPurchaseRecordId(payLogicPurchaseRecord.getId());

        record.setUserId(payLogicPurchaseRecord.getUserId());
        record.setLoginName(payLogicPurchaseRecord.getLoginName());
        record.setCustomerId(payLogicPurchaseRecord.getCustomerId());
        record.setType(LumenType.vip.getValue());

        record.setLumenQty(payLogicPurchaseRecord.getLumenQty());
        record.setLumenLeftQty(payLogicPurchaseRecord.getLumenQty());
        record.setVipPlatForm(VipPlatform.STRIPE.getPlatformName());
        record.setMixed(false);
        record.setInvalid(false);
        if (payLogicPurchaseRecord.getTrial() == null || !payLogicPurchaseRecord.getTrial()) {
            return getPayLumenRecords(record, periodStart, monthsCount);
        }
        return getPayLumenRecordsTrial(payLogicPurchaseRecord.getTrialDays(), periodStart, record);
    }

    @NotNull
    private static ArrayList<PayLumenRecord> getPayLumenRecordsTrial(Integer trialDays, long periodStart, PayLumenRecord srcLumenRecord) {
        Instant instant = Instant.ofEpochSecond(periodStart);
        // 将 Instant 转换为 ZonedDateTime 并设置为 UTC 时区
        ZonedDateTime startOfMonth = instant.atZone(ZoneOffset.UTC);
        startOfMonth = startOfMonth.toLocalDate().atStartOfDay(startOfMonth.getZone());
        ZonedDateTime endOfMonthTime = startOfMonth.plusDays(trialDays);
        endOfMonthTime = endOfMonthTime.toLocalDate().atTime(23, 59, 59, 0).atZone(endOfMonthTime.getZone());

        PayLumenRecord record = new PayLumenRecord();
        record.setCreateTime(record.getCreateTime());
        record.setPayLogicPurchaseRecordId(srcLumenRecord.getPayLogicPurchaseRecordId());

        record.setUserId(srcLumenRecord.getUserId());
        record.setLoginName(srcLumenRecord.getLoginName());
        record.setCustomerId(srcLumenRecord.getCustomerId());
        record.setOriginalTransactionId(srcLumenRecord.getOriginalTransactionId());
        record.setTransactionId(srcLumenRecord.getTransactionId());
        record.setType(LumenType.vip.getValue());

        long endSecond = endOfMonthTime.toEpochSecond();
        long periodStartDateTime = startOfMonth.toEpochSecond();

        record.setCurrentPeriodStart(periodStartDateTime);
        record.setCurrentPeriodEnd(endSecond);
        record.setLogicPeriodStart(periodStartDateTime);
        record.setLogicPeriodEnd(endSecond);

        record.setLumenQty(srcLumenRecord.getLumenQty());
        record.setLumenLeftQty(srcLumenRecord.getLumenQty());
        record.setVipPlatForm(srcLumenRecord.getVipPlatForm());
        record.setMixed(false);
        record.setInvalid(false);
        record.setCreateTime(srcLumenRecord.getCreateTime());
        return Lists.newArrayList(record);
    }

    @NotNull
    private static List<PayLumenRecord> getPayLumenRecords(PayLumenRecord srcLumenRecord, long periodStart, Integer monthsCount) {
        List<PayLumenRecord> periods = new ArrayList<>();
        // 将时间戳转换为 Instant
        Instant instant = Instant.ofEpochSecond(periodStart);

        // 将 Instant 转换为 ZonedDateTime 并设置为 UTC 时区
        ZonedDateTime startOfMonth = instant.atZone(ZoneOffset.UTC);
        startOfMonth = startOfMonth.toLocalDate().atStartOfDay(startOfMonth.getZone());
        int dayOfMonth = startOfMonth.getDayOfMonth();

        for (int i = 0; i < monthsCount; i++) {
            // 计算每个周期的开始和结束时间
            ZonedDateTime endOfMonthTime = startOfMonth.plusMonths(1);
            switch (dayOfMonth) {
                case 31:
                    // 如果是 31 天的月份，则结束时间为该月最后一天
                    endOfMonthTime = endOfMonthTime.with(TemporalAdjusters.lastDayOfMonth());
                    break;
                case 30:
                    Month month = endOfMonthTime.getMonth();
                    if (Month.FEBRUARY == month) {
                        endOfMonthTime = endOfMonthTime.with(TemporalAdjusters.lastDayOfMonth());
                    } else {
                        endOfMonthTime = endOfMonthTime.withDayOfMonth(30);
                    }
                    break;
                case 29:
                    if (endOfMonthTime.getMonth() == Month.FEBRUARY) {
                        endOfMonthTime = endOfMonthTime.with(TemporalAdjusters.lastDayOfMonth());
                    }
                    break;
                default:
                    break;
            }
            endOfMonthTime = endOfMonthTime.toLocalDate().atTime(23, 59, 59, 0).atZone(endOfMonthTime.getZone());
            long endSecond = endOfMonthTime.toEpochSecond();
            // 计算开始时间和结束时间的时间戳
            long periodStartDateTime = startOfMonth.toEpochSecond();
            log.info("generatePeriods {}  ", startOfMonth + " ~~ " + endOfMonthTime.toLocalDate().atTime(23, 59, 59, 0)
                    .atZone(endOfMonthTime.getZone()));

            startOfMonth = endOfMonthTime;
            PayLumenRecord record = new PayLumenRecord();
            record.setCreateTime(record.getCreateTime());
            record.setPayLogicPurchaseRecordId(srcLumenRecord.getPayLogicPurchaseRecordId());

            record.setUserId(srcLumenRecord.getUserId());
            record.setLoginName(srcLumenRecord.getLoginName());
            record.setCustomerId(srcLumenRecord.getCustomerId());
            record.setOriginalTransactionId(srcLumenRecord.getOriginalTransactionId());
            record.setTransactionId(srcLumenRecord.getTransactionId());
            record.setType(LumenType.vip.getValue());

            record.setCurrentPeriodStart(periodStartDateTime);
            record.setCurrentPeriodEnd(endSecond);
            record.setLogicPeriodStart(periodStartDateTime);
            record.setLogicPeriodEnd(endSecond);

            record.setLumenQty(srcLumenRecord.getLumenQty());
            record.setLumenLeftQty(srcLumenRecord.getLumenQty());
            record.setVipPlatForm(srcLumenRecord.getVipPlatForm());
            record.setMixed(false);
            record.setInvalid(false);
            record.setCreateTime(srcLumenRecord.getCreateTime());
            periods.add(record);
        }
        return periods;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void giftLumen(String loginName, Integer lumen) {
        // 赠送lumen
        PayLumenRecord payLumenRecord = new PayLumenRecord();
        payLumenRecord.setVipPlatForm(VipPlatform.GIFT.getPlatformName());
        payLumenRecord.setType(LumenType.gift.getValue());
        payLumenRecord.setLoginName(loginName);
        payLumenRecord.setLumenQty(lumen);
        payLumenRecord.setLumenLeftQty(lumen);
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
        ZonedDateTime zonedDateTime = now.plusDays(60);
        ZonedDateTime endOfDay = zonedDateTime.toLocalDate().atTime(23, 59, 59, 999999999).atZone(now.getZone());
        payLumenRecord.setCurrentPeriodStart(now.toEpochSecond());
        payLumenRecord.setCurrentPeriodEnd(endOfDay.toEpochSecond());
        payLumenRecord.setLogicPeriodStart(now.toEpochSecond());
        payLumenRecord.setLogicPeriodEnd(endOfDay.toEpochSecond());
        insertPayLumenRecord(payLumenRecord);
    }

    @Override
    public boolean hasPurchasedLumen(Long userId) {
        String hashKey = userId.toString();
        String cacheKey = "user:first:lumen_flag";
        // 先从 Redis 获取
        boolean hasBuy = redisService.hasHashField(cacheKey, hashKey);
        if (!hasBuy) {
            PayLumenRecord one = this.lambdaQuery().eq(PayLumenRecord::getUserId, userId)
                    .eq(PayLumenRecord::getType, LumenType.recharge.getValue())
                    .gt(PayLumenRecord::getId, 1928269958249353217L)
                    .last("limit 1")
                    .one();
            if (one != null) {
                hasBuy = true;
                redisService.putDataToHash(cacheKey, hashKey, true, 15, TimeUnit.DAYS);
            }
        }
        return hasBuy;
    }

    @Override
    public void invalidatedSubscriptionForPaypal(String srcSubscriptionId, Long payLogicId) {
        this.lambdaUpdate().eq(PayLumenRecord::getPayLogicPurchaseRecordId, payLogicId)
                .eq(PayLumenRecord::getInvalid, false)
                .set(PayLumenRecord::getInvalid, true)
                .set(PayLumenRecord::getUpdateTime, LocalDateTime.now())
                .update();
        subscriptionCurrentService.invalidatedSubscriptionForPaypal(srcSubscriptionId);
    }

    @Override
    public void reActivedSubscriptionForPaypal(String subscriptionId, Long palLogicSubscriptionId) {
        this.lambdaUpdate().eq(PayLumenRecord::getPayLogicPurchaseRecordId, palLogicSubscriptionId)
                .eq(PayLumenRecord::getInvalid, true)
                .set(PayLumenRecord::getInvalid, false)
                .set(PayLumenRecord::getUpdateTime, LocalDateTime.now())
                .update();
        subscriptionCurrentService.reActivedSubscriptionForPaypal(subscriptionId);
    }

    public void insertPayLumenRecord(PayLumenRecord payLumenRecord) {
        payLumenRecord.setCreateTime(LocalDateTime.now());
        payLumenRecord.setLumenLeftQty(payLumenRecord.getLumenQty());
        Long currentTimestamp = DateUtils.getTimestamp(LocalDateTime.now());
        // 如果在当前订阅期限内，清空 Redis 缓存
        if (payLumenRecord.getLogicPeriodStart() <= currentTimestamp && currentTimestamp <= payLumenRecord.getLogicPeriodEnd()) {
            log.info("在当前订阅期限内，清空 Redis 缓存");
            VipService vipService = applicationContext.getBean(VipService.class);
            vipService.resettingPersonalLumens(payLumenRecord.getLoginName());
        }
        this.save(payLumenRecord);
    }

}
