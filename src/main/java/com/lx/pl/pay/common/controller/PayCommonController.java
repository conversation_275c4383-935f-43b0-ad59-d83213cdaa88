package com.lx.pl.pay.common.controller;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.dto.SubscriptionCurrentDto;
import com.lx.pl.pay.common.service.CommonProductService;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.service.VipService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@Tag(name = "vip共通相关接口")
@RequestMapping("/api/pay/common")
public class PayCommonController {

    @Autowired
    VipService vipService;
    @Autowired
    SubscriptionCurrentService subscriptionCurrentService;
    @Autowired
    CommonProductService commonProductService;

    @Operation(summary = "查询用户vip信息详情for pay")
    @Authorization
    @GetMapping(path = "/vip-info-for-pay")
    public R<Map<String, Object>> vipInfo(@Parameter(hidden = true) @CurrentUser User user, String vipPlatform) {
        return R.success(vipService.getRealUserVipInfo(user.getId(), vipPlatform));
    }

    @Operation(summary = "查询支付用所有激活vip信息详情")
    @Authorization
    @GetMapping(path = "/all-pay-vip-info")
    public R<List<SubscriptionCurrentDto>> getAllPayVipInfo(@Parameter(hidden = true) @CurrentUser User user) {
        List<SubscriptionCurrent> validSubscriptionsFromDb = subscriptionCurrentService.getValidSubscriptionsFromDb(user.getId());
        List<SubscriptionCurrentDto> subscriptionCurrentDtos = validSubscriptionsFromDb.stream()
                .map(sub -> {
                    SubscriptionCurrentDto dto = new SubscriptionCurrentDto();
                    dto.setCurrentPeriodStart(sub.getCurrentPeriodStart());
                    dto.setCurrentPeriodEnd(sub.getCurrentPeriodEnd());
                    dto.setPlanLevel(sub.getPlanLevel());
                    dto.setPriceInterval(sub.getPriceInterval());
                    dto.setVipBeginTime(sub.getVipBeginTime());
                    dto.setVipEndTime(sub.getVipEndTime());
                    dto.setVipPlatform(sub.getVipPlatform());
                    dto.setAutoRenewStatus(sub.getAutoRenewStatus());
                    dto.setMark(sub.getMark());
                    dto.setRenewPrice(sub.getRenewPrice());
                    dto.setTrial(sub.getTrial());
                    return dto;
                })
                .collect(Collectors.toList());
        // 按条件分组，保留每组中 vipEndTime 最大的数据
        Map<List<String>, Optional<SubscriptionCurrentDto>> groupedMap = subscriptionCurrentDtos.stream()
                .collect(Collectors.groupingBy(
                        dto -> Arrays.asList(dto.getVipPlatform(), dto.getPlanLevel(), dto.getPriceInterval()),
                        Collectors.maxBy(Comparator.comparing(SubscriptionCurrentDto::getVipEndTime))
                ));

        List<SubscriptionCurrentDto> filteredList = groupedMap.values().stream()
                .filter(Optional::isPresent) // 过滤掉空值
                .map(Optional::get)          // 提取实际值
                .collect(Collectors.toList());
        return R.success(filteredList.stream().sorted(Comparator.comparing((SubscriptionCurrentDto sub) -> {
            // 设置 vipType 的优先级，转换为整数
            switch (sub.getPlanLevel()) {
                case "pro":
                    return 3;    // pro 为最高
                case "standard":
                    return 2;    // standard 为中等
                case "basic":
                    return 1;    // basic 为最低
                default:
                    return 1;    // 未知类型
            }
        }).thenComparing(sub -> {
            // 设置 priceInterval 的优先级，年付 > 月付
            return "year".equals(sub.getPriceInterval()) ? 1 : 0;
        }).reversed()).collect(Collectors.toList()));


    }
}
