package com.lx.pl.pay.common.controller;

import com.lx.pl.pay.common.service.PromotionConfigService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 优惠配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Slf4j
//@RestController
//@RequestMapping("/pay/promotion-config")
@Tag(name = "优惠配置管理", description = "优惠配置相关接口")
public class PromotionConfigController {

//    @Autowired
    private PromotionConfigService promotionConfigService;

//    @Operation(summary = "分页查询优惠配置")
//    @GetMapping("/page")
//    public R<IPage<PromotionConfig>> page(
//            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
//            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
//            @Parameter(description = "优惠类型") @RequestParam(required = false) String type,
//            @Parameter(description = "优惠名称") @RequestParam(required = false) String name,
//            @Parameter(description = "是否启用") @RequestParam(required = false) Boolean enable) {
//
//        Page<PromotionConfig> page = new Page<>(current, size);
//        LambdaQueryWrapper<PromotionConfig> queryWrapper = new LambdaQueryWrapper<>();
//
//        if (StringUtils.hasText(type)) {
//            queryWrapper.eq(PromotionConfig::getType, type);
//        }
//        if (StringUtils.hasText(name)) {
//            queryWrapper.like(PromotionConfig::getName, name);
//        }
//        if (enable != null) {
//            queryWrapper.eq(PromotionConfig::getEnable, enable);
//        }
//
//        queryWrapper.orderByDesc(PromotionConfig::getCreateTime);
//
//        IPage<PromotionConfig> result = promotionConfigService.page(page, queryWrapper);
//        return R.success(result);
//    }
//
//    @Operation(summary = "根据ID查询优惠配置")
//    @GetMapping("/{id}")
//    public R<PromotionConfig> getById(@Parameter(description = "配置ID") @PathVariable Long id) {
//        PromotionConfig promotionConfig = promotionConfigService.getById(id);
//        if (promotionConfig == null) {
//            return R.fail(0,"优惠配置不存在");
//        }
//        return R.success(promotionConfig);
//    }
//
//    @Operation(summary = "根据类型查询有效的优惠配置")
//    @GetMapping("/valid/{type}")
//    public R<List<PromotionConfig>> getValidByType(@Parameter(description = "优惠类型") @PathVariable String type) {
//        List<PromotionConfig> promotions = promotionConfigService.getValidPromotionsByType(type);
//        return R.success(promotions);
//    }
//
//    @Operation(summary = "获取所有有效的优惠配置")
//    @GetMapping("/valid")
//    public R<List<PromotionConfig>> getAllValid() {
//        List<PromotionConfig> promotions = promotionConfigService.getAllValidPromotions();
//        return R.success(promotions);
//    }
//
//    @Operation(summary = "创建优惠配置")
//    @PostMapping
//    public R<String> create(@Valid @RequestBody PromotionConfigDto dto) {
//        PromotionConfig promotionConfig = new PromotionConfig();
//        BeanUtils.copyProperties(dto, promotionConfig);
//
//        boolean success = promotionConfigService.saveOrUpdatePromotion(promotionConfig);
//        if (success) {
//            return R.success("创建成功");
//        } else {
//            return R.fail(0,"创建失败");
//        }
//    }
//
//    @Operation(summary = "更新优惠配置")
//    @PutMapping("/{id}")
//    public R<String> update(@Parameter(description = "配置ID") @PathVariable Long id,
//                           @Valid @RequestBody PromotionConfigDto dto) {
//        PromotionConfig existingConfig = promotionConfigService.getById(id);
//        if (existingConfig == null) {
//            return R.fail(0,"优惠配置不存在");
//        }
//
//        dto.setId(id);
//        PromotionConfig promotionConfig = new PromotionConfig();
//        BeanUtils.copyProperties(dto, promotionConfig);
//
//        boolean success = promotionConfigService.saveOrUpdatePromotion(promotionConfig);
//        if (success) {
//            return R.success("更新成功");
//        } else {
//            return R.fail(0,"更新失败");
//        }
//    }
//
//    @Operation(summary = "删除优惠配置")
//    @DeleteMapping("/{id}")
//    public R<String> delete(@Parameter(description = "配置ID") @PathVariable Long id) {
//        boolean success = promotionConfigService.removeById(id);
//        if (success) {
//            return R.success("删除成功");
//        } else {
//            return R.fail(0,"删除失败");
//        }
//    }
//
//    @Operation(summary = "启用/禁用优惠配置")
//    @PutMapping("/{id}/enable")
//    public R<String> enablePromotion(@Parameter(description = "配置ID") @PathVariable Long id,
//                                    @Parameter(description = "是否启用") @RequestParam Boolean enable) {
//        boolean success = promotionConfigService.enablePromotion(id, enable);
//        if (success) {
//            return R.success(enable ? "启用成功" : "禁用成功");
//        } else {
//            return R.fail(0,enable ? "启用失败" : "禁用失败");
//        }
//    }
//
//    @Operation(summary = "根据优惠券ID查询配置")
//    @GetMapping("/coupon/{couponId}")
//    public R<PromotionConfig> getByCouponId(@Parameter(description = "优惠券ID") @PathVariable String couponId) {
//        PromotionConfig promotionConfig = promotionConfigService.getByCouponId(couponId);
//        if (promotionConfig == null) {
//            return R.fail(0,"优惠配置不存在");
//        }
//        return R.success(promotionConfig);
//    }
}
