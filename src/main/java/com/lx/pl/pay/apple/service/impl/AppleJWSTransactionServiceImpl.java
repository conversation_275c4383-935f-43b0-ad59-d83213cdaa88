package com.lx.pl.pay.apple.service.impl;

import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.apple.itunes.storekit.model.Type;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.apple.domain.AppleJWSTransaction;
import com.lx.pl.pay.apple.mapper.AppleJWSTransactionMapper;
import com.lx.pl.pay.apple.service.AppleJWSTransactionService;
import com.lx.pl.pay.common.util.PayLogContextHolder;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class AppleJWSTransactionServiceImpl extends ServiceImpl<AppleJWSTransactionMapper, AppleJWSTransaction> implements AppleJWSTransactionService {
    // 基础的 CRUD 操作由 ServiceImpl 提供
    Logger log = LoggerFactory.getLogger("apple-pay-msg");

    @Override
    public boolean existsByTransactionId(String transactionId) {
        // 使用 lambdaQuery 构建查询条件，检查是否存在指定 transactionId 的记录
        return this.lambdaQuery()
                .eq(AppleJWSTransaction::getTransactionId, transactionId)
                .exists();
    }

    @Override
    public boolean existsByTransactionIdWithUserId(String transactionId) {
        // 使用 lambdaQuery 构建查询条件，检查是否存在指定 transactionId 的记录
        return this.lambdaQuery().eq(AppleJWSTransaction::getTransactionId, transactionId).gt(AppleJWSTransaction::getUserId, 0).exists();
    }

    @Override
    public AppleJWSTransaction getLastProcessedTransactionTime(String originalTransactionId) {
        // 使用 lambdaQuery 查询指定 originalTransactionId 的最后一条记录的购买时间
        AppleJWSTransaction lastTransaction = this.lambdaQuery().eq(AppleJWSTransaction::getOriginalTransactionId, originalTransactionId).orderByDesc(AppleJWSTransaction::getPurchaseDate)
                .last("LIMIT 1").one();
        // 如果找到记录则返回购买时间，否则返回 null
        return lastTransaction;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTransaction(JWSTransactionDecodedPayload payload, User user) {

        log.info("start 保存交易记录：transactionId={}", payload.getTransactionId());
        AppleJWSTransaction transaction = new AppleJWSTransaction();
        transaction.setLogUUID(PayLogContextHolder.getLogUUID());
        // 设置 transaction 的属性
        transaction.setTransactionId(payload.getTransactionId());
        transaction.setOriginalTransactionId(payload.getOriginalTransactionId());
        transaction.setProductId(payload.getProductId());
        transaction.setWebOrderLineItemId(payload.getWebOrderLineItemId());
        transaction.setSubscriptionGroupIdentifier(payload.getSubscriptionGroupIdentifier());
        if (payload.getPurchaseDate() != null) {
            transaction.setPurchaseDate(payload.getPurchaseDate() / 1000);
        }
        transaction.setPurchaseDateMs(payload.getPurchaseDate());
        if (payload.getOriginalPurchaseDate() != null) {
            transaction.setOriginalPurchaseDate(payload.getOriginalPurchaseDate() / 1000);
        }
        transaction.setOriginalPurchaseDateMs(payload.getOriginalPurchaseDate());
        if (payload.getExpiresDate() != null) {
            transaction.setExpiresDate(payload.getExpiresDate() / 1000);
        }
        transaction.setQuantity(payload.getQuantity());
        transaction.setType(payload.getType().getValue());
        if (payload.getInAppOwnershipType() != null) {
            transaction.setInAppOwnershipType(payload.getInAppOwnershipType().getValue());
        }
        if (payload.getRevocationDate() != null) {
            transaction.setRevocationDate(payload.getRevocationDate() / 1000);
        }
        transaction.setRevocationDateMs(payload.getRevocationDate());
        if (payload.getRevocationReason() != null) {
            transaction.setRevocationReason(payload.getRevocationReason().getValue());
        }
        transaction.setIsUpgraded(payload.getIsUpgraded());
        transaction.setOfferIdentifier(payload.getOfferIdentifier());
        if (payload.getOfferType() != null) {
            transaction.setOfferType(payload.getOfferType().getValue());
        }
        if (payload.getEnvironment() != null) {
            transaction.setEnvironment(payload.getEnvironment().getValue());
        }
        transaction.setStorefront(payload.getStorefront());
        transaction.setStorefrontId(payload.getStorefrontId());
        if (payload.getTransactionReason() != null) {
            transaction.setTransactionReason(payload.getTransactionReason().getValue());
        }
        transaction.setCurrency(payload.getCurrency());
        transaction.setPrice(payload.getPrice());
        if (payload.getOfferDiscountType() != null) {
            transaction.setOfferDiscountType(payload.getOfferDiscountType().getValue());
        }
        transaction.setSignedDateMs(payload.getSignedDate());
        if (payload.getAppAccountToken() != null) {
            transaction.setAppAccountToken(payload.getAppAccountToken().toString());
        }
        transaction.setBundleId(payload.getBundleId());
        if (user != null) {
            transaction.setUserId(user.getId());
            transaction.setLoginName(user.getLoginName());
        }
        transaction.setCreateTime(LocalDateTime.now());
        AppleJWSTransaction appleJWSTransaction = this.queryByTransactionId(transaction.getTransactionId());
        if (appleJWSTransaction == null) {
            this.save(transaction);
        } else {
            transaction.setId(appleJWSTransaction.getId());
            this.updateById(transaction);
        }

        log.info("end 保存交易记录：transactionId={}", payload.getTransactionId());
    }

    @Override
    public List<AppleJWSTransaction> querySubscriptionListByOriginalTransactionId(String originalTransactionIde) {
        return this.lambdaQuery().eq(AppleJWSTransaction::getOriginalTransactionId, originalTransactionIde).eq(AppleJWSTransaction::getType, Type.AUTO_RENEWABLE_SUBSCRIPTION.getValue()).list();

    }

    @Override
    public AppleJWSTransaction queryByTransactionId(String transactionId) {
        return this.lambdaQuery().eq(AppleJWSTransaction::getTransactionId, transactionId).one();
    }

    @Override
    public void updateTransaction(JWSTransactionDecodedPayload transaction, User o) {
        log.info("start 更新交易记录：transactionId={}", transaction.getTransactionId());
        String loginName = o != null ? o.getLoginName() : null;
        Long userId = o != null ? o.getId() : null;
        this.lambdaUpdate().eq(AppleJWSTransaction::getTransactionId, transaction.getTransactionId())
                .set(AppleJWSTransaction::getRevocationReason, transaction.getRevocationReason() != null ? transaction.getRevocationReason().getValue() : null)
                .set(AppleJWSTransaction::getRevocationDate, transaction.getRevocationDate() != null ? transaction.getRevocationDate() / 1000 : null)
                .set(AppleJWSTransaction::getRevocationDateMs, transaction.getRevocationDate())
                .set(AppleJWSTransaction::getUserId, userId)
                .set(AppleJWSTransaction::getLoginName, loginName)
                .set(AppleJWSTransaction::getUpdateTime, LocalDateTime.now())
                .update();
    }
}