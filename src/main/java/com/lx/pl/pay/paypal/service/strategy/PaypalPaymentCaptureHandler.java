package com.lx.pl.pay.paypal.service.strategy;

import cn.hutool.core.util.StrUtil;
import com.lx.pl.enums.AlarmEnum;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.paypal.annotation.PaypalEvent;
import com.lx.pl.pay.paypal.model.PaymentCaptureDetails;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentRecord;
import com.lx.pl.pay.paypal.model.domain.PayPalRefundRecord;
import com.lx.pl.pay.paypal.model.event.PaypalPaymentCaptureEvent;
import com.lx.pl.pay.paypal.service.IPaypalEventHandler;
import com.lx.pl.pay.paypal.service.PayPalOrderPaymentRecordService;
import com.lx.pl.pay.paypal.service.PayPalRefundRecordService;
import com.lx.pl.service.message.DingTalkAlert;
import com.lx.pl.util.LogicUtil;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 *
 */
@Component
@PaypalEvent(eventType = {"PAYMENT.CAPTURE.COMPLETED", "PAYMENT.CAPTURE.PENDING", "PAYMENT.CAPTURE.DENIED", "PAYMENT.CAPTURE.REVERSED"})
public class PaypalPaymentCaptureHandler extends IPaypalEventHandler<PaypalPaymentCaptureEvent> {

    @Autowired
    private PayLumenRecordService payLumenRecordService;
    @Autowired
    private PayPalOrderPaymentRecordService paymentOrderPaymentRecordService;
    @Autowired
    private PayPalRefundRecordService payPalRefundRecordService;
    @Resource
    private DingTalkAlert dingTalkAlert;

    @Override
    public void handleEvent(PaypalPaymentCaptureEvent data) {
        PaymentCaptureDetails captureModel = data.getModel();
        // relatedIds 做锁的key
        PaymentCaptureDetails.SupplementaryData supplementaryData = captureModel.getSupplementaryData();
        String key = captureModel.getId();
        if (supplementaryData != null && supplementaryData.getRelatedIds() != null) {
            log.info("relatedIds is inot null");
            key = supplementaryData.getRelatedIds().getOrderId();
        }

        log.info("lock key {}", PAYPAL_ACTION_LOCK_PREFIX + key);
        RLock lock = redissonClient.getLock(PAYPAL_ACTION_LOCK_PREFIX + key);
        String loginName = null;
        try {
            lock.lock();
            loginName = applicationContext.getBean(PaypalPaymentCaptureHandler.class).doHandleEvent(data);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
            }
        } finally {
            unlockAndRefreshVip(lock, loginName);
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public String doHandleEvent(PaypalPaymentCaptureEvent data) {
        PaymentCaptureDetails model = data.getModel();
        if (!Objects.equals(model.getStatus(), "COMPLETED")) {
            //发送告警信息
            dingTalkAlert.sendAlarmMessage(LogicUtil.buildAlarmMessage(
                    AlarmEnum.AlarmTypeEnum.BUSINESS.getDescription(),
                    AlarmEnum.AlarmSourceEnum.PAY_PAL.getDescription(),
                    "PayPal capture status alarm, id: " + model.getId() + ", status: " + model.getStatus(),
                    LogicUtil.getUserIdByPayPalEvent(data),
                    null));
        }
        switch (model.getStatus()) {
            case "PENDING":
            case "COMPLETED":
                return handleCompletedPayment(data.getModel());
            case "REFUNDED":
                recordRefund(data);
            case "DECLINED":
            case "FAILED":
                return handleDeniedPayment(data.getModel());
            case "PARTIALLY_REFUNDED":
                log.info("PARTIALLY_REFUNDED: {}", model.toJSON());
                // 记录退款记录
                recordRefund(data);
            default:
                handleOtherPayment(data.getModel());
                return null;
        }
    }

    private void recordRefund(PaypalPaymentCaptureEvent event) {
        PaymentCaptureDetails model = event.getModel();
        log.info("recordRefund: {}", model.toJSON());
        PayPalRefundRecord payPalRefundRecord = new PayPalRefundRecord();
        payPalRefundRecord.setPaymentId(model.getId());
        payPalRefundRecord.setCreateTime(LocalDateTime.now());
        payPalRefundRecord.setLink(model.getLinks().toString());
        payPalRefundRecord.setRefundReasonCode(model.getStatus());
        payPalRefundRecord.setSaleId(model.getId());
        payPalRefundRecord.setStatus(model.getStatus());
        payPalRefundRecord.setTotal(model.getAmount().getValue());
        payPalRefundRecord.setCurrency(model.getAmount().getCurrencyCode());
        payPalRefundRecord.setMark(event.getEventType() + "-" + model.getStatus());

        payPalRefundRecordService.save(payPalRefundRecord);

    }

    private void handleOtherPayment(PaymentCaptureDetails model) {
        log.info("handleOtherPayment: {}", model.toJSON());
        PaymentCaptureDetails.RelatedIds relatedIds = model.getSupplementaryData().getRelatedIds();
        List<PayPalOrderPaymentRecord> records = paymentOrderPaymentRecordService.findByOrderId(relatedIds.getOrderId());
        if (records != null && !records.isEmpty()) {
            paymentOrderPaymentRecordService.lambdaUpdate()
                    .eq(PayPalOrderPaymentRecord::getOrderId, records.get(0).getOrderId())
                    .set(PayPalOrderPaymentRecord::getCaptureStatus, model.getStatus())
                    .set(PayPalOrderPaymentRecord::getUpdateTime, LocalDateTime.now())
                    .update();
        }
    }


    private String handleDeniedPayment(PaymentCaptureDetails model) {
        log.info("PAYMENT.CAPTURE.DENIED: {}", model.toJSON());
        PaymentCaptureDetails.RelatedIds relatedIds = model.getSupplementaryData().getRelatedIds();
        List<PayPalOrderPaymentRecord> records = paymentOrderPaymentRecordService.findByOrderId(relatedIds.getOrderId());
        if (records != null && !records.isEmpty()) {
            paymentOrderPaymentRecordService.lambdaUpdate()
                    .eq(PayPalOrderPaymentRecord::getOrderId, records.get(0).getOrderId())
                    .set(PayPalOrderPaymentRecord::getCaptureStatus, model.getStatus())
                    .set(PayPalOrderPaymentRecord::getPaymentId, model.getId())
                    .set(model.getSellerProtection() != null, PayPalOrderPaymentRecord::getSellerProtectionStatus, model.getSellerProtection().getStatus())
                    .set(model.getSellerProtection() != null && model.getSellerProtection().getDisputeCategories() != null, PayPalOrderPaymentRecord::getDisputeCategories, model.getSellerProtection()
                            .getDisputeCategories().toString())
                    .set(PayPalOrderPaymentRecord::getUpdateTime, LocalDateTime.now())
                    .update();
            // revert lumen by order id
            payLumenRecordService.recallLumenForPaypal(records.get(0).getId(), model.getStatus() + " " + model.getId());
            return null;
        }
        return null;
    }

/*    private String handlePendingPayment(PaymentCaptureDetails model) {
        PaymentCaptureDetails.RelatedIds relatedIds = model.getSupplementaryData().getRelatedIds();
        List<PayPalOrderPaymentRecord> records = paymentOrderPaymentRecordService.findByOrderId(relatedIds.getOrderId());

        if (records != null && !records.isEmpty()) {
            // 更新订单状态为PENDING
            paymentOrderPaymentRecordService.lambdaUpdate()
                    .eq(PayPalOrderPaymentRecord::getOrderId, records.get(0).getOrderId())
                    .set(PayPalOrderPaymentRecord::getCaptureStatus, "PENDING")
                    .set(PayPalOrderPaymentRecord::getPaymentId, model.getId())
                    .set(PayPalOrderPaymentRecord::getPayCreateTime, model.getCreateTime())
                    .set(model.getSellerProtection() != null, PayPalOrderPaymentRecord::getSellerProtectionStatus, model.getSellerProtection().getStatus())
                    .set(model.getSellerProtection() != null && model.getSellerProtection().getDisputeCategories() != null, PayPalOrderPaymentRecord::getDisputeCategories, model.getSellerProtection()
                            .getDisputeCategories().toString())
                    .set(PayPalOrderPaymentRecord::getUpdateTime, LocalDateTime.now())
                    .update();

            log.info("交易处于PENDING状态，暂不发放权益: orderId={}, captureId={}",
                    relatedIds.getOrderId(), model.getId());

            // 返回登录名但不发放权益
            return null;
        } else {
            log.warn("未找到相关订单记录: orderId={}", relatedIds.getOrderId());
            return null;
        }
    }*/

    private String handleCompletedPayment(PaymentCaptureDetails model) {
        log.info("PAYMENT.CAPTURE.COMPLETED: {}", model.toJSON());
        PaymentCaptureDetails.RelatedIds relatedIds = model.getSupplementaryData().getRelatedIds();
        List<PayPalOrderPaymentRecord> byorderIds = paymentOrderPaymentRecordService.findByOrderId(relatedIds.getOrderId());
        PaymentCaptureDetails.SellerReceivableBreakdown sellerReceivableBreakdown = model.getSellerReceivableBreakdown();
        paymentOrderPaymentRecordService.lambdaUpdate()
                .eq(PayPalOrderPaymentRecord::getOrderId, byorderIds.get(0).getOrderId())
                .set(PayPalOrderPaymentRecord::getCaptureStatus, "COMPLETED")
                .set(PayPalOrderPaymentRecord::getPaymentId, model.getId())
                .set(PayPalOrderPaymentRecord::getUpdateTime, LocalDateTime.now())
                .set(sellerReceivableBreakdown != null && sellerReceivableBreakdown.getPaypalFee() != null, PayPalOrderPaymentRecord::getFee, sellerReceivableBreakdown.getPaypalFee().getValue())
                .set(sellerReceivableBreakdown != null && sellerReceivableBreakdown.getNetAmount() != null, PayPalOrderPaymentRecord::getNetAmount, sellerReceivableBreakdown.getNetAmount().getValue())
                .update();
        // 判断是否发放过权益

        payLumenRecordService.saveOneTimeLumenForPaypal(byorderIds);
        return byorderIds.get(0).getLoginName();
    }
}
