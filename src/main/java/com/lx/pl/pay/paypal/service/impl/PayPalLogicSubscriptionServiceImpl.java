package com.lx.pl.pay.paypal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.enums.AlarmEnum;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.paypal.enums.PaypalSubscriptonStatusEnum;
import com.lx.pl.pay.paypal.mapper.PayPalLogicSubscriptionMapper;
import com.lx.pl.pay.paypal.model.*;
import com.lx.pl.pay.paypal.model.domain.*;
import com.lx.pl.pay.paypal.service.*;
import com.lx.pl.service.UserService;
import com.lx.pl.service.VipService;
import com.lx.pl.service.message.DingTalkAlert;
import com.lx.pl.util.LogicUtil;
import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.PayPalRESTException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;

@Service
public class PayPalLogicSubscriptionServiceImpl extends ServiceImpl<PayPalLogicSubscriptionMapper, PayPalLogicSubscription> implements PayPalLogicSubscriptionService {

    static protected Logger log = LoggerFactory.getLogger("paypal-pay-msg");

    @Resource
    private UserService userService;
    @Resource
    private PayPalRefundRecordService payPalRefundRecordService;
    @Resource
    private PayLumenRecordService payLumenRecordService;
    @Resource
    private SubscriptionCurrentService subscriptionCurrentService;
    @Resource
    private PayPalPlanService payPalPlanService;
    @Resource
    private PayPalProductService payPalProductService;
    @Resource
    private APIContext apiContext;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private PaypalUpgradeLogService paypalUpgradeLogService;
    @Resource
    private RedissonClient redissonClient;
    private static final Executor THREAD_POOL = new ThreadPoolExecutor(Runtime.getRuntime()
            .availableProcessors(),
            Runtime.getRuntime()
                    .availableProcessors() * 2, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(2000),
            Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());
    @Autowired
    private VipService vipService;
    @Resource
    private DingTalkAlert dingTalkAlert;

    @Override
    public PayPalLogicSubscription queryBySubscriptionId(String subscriptionId) {
        PayPalLogicSubscription one = this.lambdaQuery().eq(PayPalLogicSubscription::getSubscriptionId, subscriptionId)
                .eq(PayPalLogicSubscription::getInvalid, false).one();
        return one;
    }

    public List<PayPalLogicSubscription> queryValidSubscriptions() {
        List<PayPalLogicSubscription> one = this.lambdaQuery()
                .eq(PayPalLogicSubscription::getInvalid, false)
                .eq(PayPalLogicSubscription::getStatus, "ACTIVE")
                .gt(PayPalLogicSubscription::getNextBillingSec, Instant.now().getEpochSecond())
                .list();
        return one;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayPalLogicSubscription createSubscription(PaypalSubscriptionModel subscription, String userId) {
        String subscriptionId = subscription.getId();
        PayPalLogicSubscription payPalLogicSubscription = this.queryBySubscriptionId(subscriptionId);
        if (payPalLogicSubscription != null) {
            log.info("subscriptionId: {} 已经存在", subscriptionId);
            return payPalLogicSubscription;
        }
//        String customId = subscription.getCustomId();
        if (userId == null || userId.isEmpty()) {
            log.info("customId: {} 为空", userId);
            return null;
        }
        User user = userService.getUserById(Long.valueOf(userId));
        payPalLogicSubscription = new PayPalLogicSubscription();
        payPalLogicSubscription.setSubscriptionId(subscriptionId);
        payPalLogicSubscription.setUserId(Long.valueOf(userId));
        payPalLogicSubscription.setLoginName(user.getLoginName());
        payPalLogicSubscription.setPlanId(subscription.getPlanId());
        payPalLogicSubscription.setStatus(subscription.getStatus());
        payPalLogicSubscription.setStartTime(subscription.getStartTime());
        payPalLogicSubscription.setQuantity(subscription.getQuantity());
        // UTC 2025-02-21T06:29:48Z 转时间戳秒
        String startTime = subscription.getStartTime();
        if (startTime != null) {
            payPalLogicSubscription.setStartUtcSec(Instant.parse(startTime).getEpochSecond());

        }
        payPalLogicSubscription.setSubStartSec(null);
        Subscriber subscriber = subscription.getSubscriber();
        if (subscriber != null) {
            payPalLogicSubscription.setSubscriberPayerId(subscriber.getPayerId());
            payPalLogicSubscription.setSubscriberEmail(subscriber.getEmailAddress());
        }
        payPalLogicSubscription.setInvalid(false);
        boolean save = this.save(payPalLogicSubscription);
        return save ? payPalLogicSubscription : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayPalLogicSubscription activateSubscription(PaypalSubscriptionModel model, PayPalSubPaymentRecord payment) {
        String subscriptionId = model.getId();
        String paymentId = payment == null ? null : payment.getPaymentId();
        PayPalLogicSubscription payPalLogicSubscriptionConvert = model.convert();
        payPalLogicSubscriptionConvert.setPaymentId(paymentId);
        Long userId = payPalLogicSubscriptionConvert.getUserId();
        User user = userService.getUserById(userId);
        if (user == null) {
            log.info("payPalLogicSubscriptionConvert userId: {} 不存在", userId);
            return null;
        }
        PayPalLogicSubscription payPalLogicSubscription = this.queryBySubscriptionId(subscriptionId);

        if (payPalLogicSubscription != null) {
            if ("ACTIVE".equals(payPalLogicSubscriptionConvert.getStatus())
                    && Objects.equals(payPalLogicSubscription.getNextBillingTime(), payPalLogicSubscriptionConvert.getNextBillingTime())
                    && Objects.equals(payPalLogicSubscription.getStatus(), payPalLogicSubscriptionConvert.getStatus())) {
                log.info("payPalLogicSubscriptionConvert: {} 已经存在", payPalLogicSubscriptionConvert.getSubscriptionId());
                if (payPalLogicSubscription.getPaymentId() == null && payment != null) {
                    payPalLogicSubscription.setPaymentId(paymentId);
                    this.lambdaUpdate().eq(PayPalLogicSubscription::getId, payPalLogicSubscription.getId())
                            .set(PayPalLogicSubscription::getPaymentId, paymentId)
                            .update();
                }
                log.info("payPalLogicSubscriptionConvert: {} 状态未改变", payPalLogicSubscriptionConvert.getSubscriptionId());
                return payPalLogicSubscription;
            }
            this.invalidById(payPalLogicSubscription.getId());
        }
        log.info("activateSubscription model {}", model);
        log.info("payPalLogicSubscription model {}", payPalLogicSubscription);


        payPalLogicSubscriptionConvert.setLoginName(user.getLoginName());
        payPalLogicSubscriptionConvert.setStatusChangeNote(model.getStatusChangeNote());
        this.save(payPalLogicSubscriptionConvert);
        log.info("activateSubscription payPalLogicSubscriptionConvert {}", payPalLogicSubscriptionConvert);
        PayPalProduct paypalPlan = payPalProductService.getPaypalProductByPlanId(model.getPlanId());
        log.info("activateSubscription payPalLogicSubscriptionConvert start sub {}", payPalLogicSubscriptionConvert.getSubStartSec());
        payLumenRecordService.saveLumenRecordForPayPal(payPalLogicSubscriptionConvert, paypalPlan);
        subscriptionCurrentService.saveOrUpdate(buildSubscriptionCurrent(payPalLogicSubscriptionConvert, paypalPlan, model));
        return this.queryBySubscriptionId(subscriptionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayPalLogicSubscription updateSubscription(PaypalSubscriptionModel model) {
        String subscriptionId = model.getId();
        PayPalLogicSubscription payPalLogicSubscription = this.queryBySubscriptionId(subscriptionId);
        if (payPalLogicSubscription == null) {
            log.info("subscriptionId: {} 不存在", subscriptionId);
            throw new RuntimeException("subscriptionId: " + subscriptionId + " 不存在");
        }
        String planId = model.getPlanId();
        if (payPalProductService.getPaypalProductByPlanId(planId) == null) {
            log.info("planId: {} 不存在", planId);
            throw new RuntimeException("planId: " + planId + " 不存在");
        }
        boolean upgradeOrDowngrade = !planId.equals(payPalLogicSubscription.getPlanId());

        if (upgradeOrDowngrade && model.getStatus().equals("ACTIVE")) {
            log.info("subscriptionId: {} planId: {}  srcPlanId: {} 升级或降级", subscriptionId, planId, payPalLogicSubscription.getPlanId());
            this.invalidById(payPalLogicSubscription.getId());
            PayPalLogicSubscription newSubscription = copyToNew(payPalLogicSubscription);
            newSubscription.setInvalid(false);
            newSubscription.setPlanId(planId);
            newSubscription.setStartUtcSec(Instant.parse(model.getStartTime()).getEpochSecond());
            newSubscription.setQuantity(1);
            newSubscription.setStatus(model.getStatus());
            newSubscription.setStatusChangeNote(model.getStatusChangeNote());
            BillingInfo billingInfo = model.getBillingInfo();
            if (billingInfo != null) {
                newSubscription.setNextBillingSec(Instant.parse(billingInfo.getNextBillingTime()).getEpochSecond());
                newSubscription.setNextBillingTime(billingInfo.getNextBillingTime());
            }
            this.save(newSubscription);
            return newSubscription;
        } else {
            log.info("subscriptionId: {} planId: {}  srcPlanId: {} 升级或降级 status:{}", subscriptionId, planId, payPalLogicSubscription.getPlanId(), model.getStatus());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String calculateVipByPayment(PayPalSubPaymentRecord payPalSubPaymentRecord) {
        String subscriptionId = payPalSubPaymentRecord.getSubscriptionId();
        if (subscriptionId == null) {
            log.info("subscriptionId: {} 不存在 {}", payPalSubPaymentRecord.getPaymentId(), payPalSubPaymentRecord.getId());
            return null;
        }
        // boolean
        PaypalSubscriptionModel subscriptionModel = queryPaypalSubDetail(subscriptionId);
        PayPalLogicSubscription payPalLogicSubscription = this.activateSubscription(subscriptionModel, payPalSubPaymentRecord);
        if (payPalLogicSubscription == null) {
            return null;
        }
        String s = MDC.get("TRACE_ID");
        THREAD_POOL.execute(() -> {
            MDC.put("TRACE_ID", s);
            try {
                PayPalRefundRecord payPalRefundRecord = payPalRefundRecordService.findByPaymentId(payPalSubPaymentRecord.getPaymentId());
                if (payPalRefundRecord != null) {
                    log.info("calculateVipByPayment payPalRefundRecord has been refund : {}", payPalRefundRecord);
                    return;
                }
                log.info("calculateVipByPayment payPalLogicSubscription: {}", payPalLogicSubscription);
                Long userId = payPalLogicSubscription.getUserId();
                User user = userService.getUserById(userId);
                PaypalUpgradeLog one = paypalUpgradeLogService.lambdaQuery()
                        .eq(PaypalUpgradeLog::getNewSubscriptionId, subscriptionId)
                        .eq(PaypalUpgradeLog::getHasRefund, false)
                        .notIn(PaypalUpgradeLog::getSrcSubStatus, PaypalSubscriptonStatusEnum.CANCELLED.getStatus(), PaypalSubscriptonStatusEnum.EXPIRED.getStatus())
                        .one();
                if (one != null) {
                    long nowTimeSec = Instant.now().getEpochSecond();
                    paypalUpgradeLogService.lambdaUpdate()
                            .eq(PaypalUpgradeLog::getId, one.getId())
                            .set(PaypalUpgradeLog::getNewSubActiveTime, nowTimeSec)
                            .update();
                    one.setNewSubActiveTime(nowTimeSec);
                    doHandleCancel(one, subscriptionModel, user);
                }
            } finally {
                MDC.remove("TRACE_ID");
            }
        });
        return payPalLogicSubscription.getLoginName();
    }

    @Override
    public String suspendedSubscription(PaypalSubscriptionModel model) {
        String subscriptionId = model.getId();
        PayPalLogicSubscription payPalLogicSubscription = this.queryBySubscriptionId(subscriptionId);
        this.lambdaUpdate().eq(PayPalLogicSubscription::getId, payPalLogicSubscription.getId())
                .set(PayPalLogicSubscription::getStatus, model.getStatus())
                .update();
        payLumenRecordService.invalidatedSubscriptionForPaypal(subscriptionId, payPalLogicSubscription.getId());
        PaypalSubscriptionModel paypalSubscriptionModel = PaypalSubscriptionModel.builder()
                .id(payPalLogicSubscription.getSubscriptionId())
                .reason("Customer payment failed")
                .build();
        try {
            PaypalSubscriptionModel.cancel(apiContext, paypalSubscriptionModel);
        } catch (PayPalRESTException e) {
            log.error("cancel subscriptionId: {} error", subscriptionId, e);
            throw new RuntimeException(e);
        }
        return payPalLogicSubscription.getLoginName();
    }

    @Override
    public String reActivedSubscription(PaypalSubscriptionModel model) {
        String subscriptionId = model.getId();
        PayPalLogicSubscription convert = model.convert();
        PayPalLogicSubscription payPalLogicSubscription = this.queryBySubscriptionId(subscriptionId);
        this.lambdaUpdate().eq(PayPalLogicSubscription::getId, payPalLogicSubscription.getId())
                .set(PayPalLogicSubscription::getStatus, model.getStatus())
                .set(PayPalLogicSubscription::getNextBillingTime, convert.getNextBillingTime())
                .set(PayPalLogicSubscription::getNextBillingSec, convert.getNextBillingSec())
                .set(PayPalLogicSubscription::getStatusUpdateTimeSec, Instant.parse(model.getStatusUpdateTime()).getEpochSecond())
                .set(PayPalLogicSubscription::getUpdateTime, LocalDateTime.now())
                .update();
        payLumenRecordService.reActivedSubscriptionForPaypal(subscriptionId, payPalLogicSubscription.getId());
        return "";
    }

    public void doHandleCancel(PaypalUpgradeLog one, PaypalSubscriptionModel subscriptionModel, User user) {
        RLock rLock = redissonClient.getLock("paypal:user:" + user.getId());
        try {
            rLock.lock();
            applicationContext.getBean(PayPalLogicSubscriptionServiceImpl.class)
                    .invalidOldSubscriptionIfNeed(one, subscriptionModel, user.getId());
        } catch (Exception e) {
            log.error("paypal:upgrade:{}", one.getId(), e);
            //发送告警信息
            sendAlarmMessage("PayPal upgrade exception, id: " + one.getId(), user.getId(), e);
        } finally {
            vipService.resettingPersonalLumens(user.getLoginName());
            updateUserVipStatus(user.getId());
            subscriptionCurrentService.clearUserCache(user.getId());
            if (rLock.isHeldByCurrentThread() && rLock.isLocked()) {
                rLock.unlock();
            }
        }
    }

    private void updateUserVipStatus(Long userId) {
        SubscriptionCurrent subscription = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(userId);
        log.info("更新用户VIP等级 start: loginName={} newPlanLevel={} priceInterval={}", subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
        userService.updateUserVipInfo(subscription, userId);
        log.info("更新用户VIP等级 end: loginName={}, newPlanLevel={} priceInterval={}", subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
    }

    public void invalidOldSubscriptionIfNeed(PaypalUpgradeLog upgradeLog, PaypalSubscriptionModel subscriptionModelNew, Long userId) {
        if (upgradeLog == null) {
            return;
        }
        if (upgradeLog.getHasRefund() && upgradeLog.getVipRevert()) {
            log.info("new subscriptionId: {} srcSubStatus: {}", upgradeLog.getNewSubscriptionId(), upgradeLog.getSrcSubStatus());
            return;
        }
        if (upgradeLog.getRefundId() != null) {
            log.info("new subscriptionId: {} refundId: {}", upgradeLog.getNewSubscriptionId(), upgradeLog.getRefundId());
            return;
        }

        String srcSubscriptionId = null;
        try {
            PaypalSubscriptionModel detail = PaypalSubscriptionModel.detail(apiContext, upgradeLog.getSrcSubscriptionId());
            srcSubscriptionId = upgradeLog.getSrcSubscriptionId();

            PayPalLogicSubscription convert = detail.convert();
            log.info("src subscriptionId: {} srcSubStatus1: {}", srcSubscriptionId, upgradeLog.getSrcSubStatus());
            if (!PaypalSubscriptonStatusEnum.CANCELLED.getStatus().equals(upgradeLog.getSrcSubStatus()) && convert.getNextBillingSec() > System.currentTimeMillis() / 1000) {
                PaypalSubscriptionModel userUpgradePlan = PaypalSubscriptionModel.builder().reason("user upgrade plan").id(srcSubscriptionId).build();
                try {
                    PaypalSubscriptionModel.cancel(apiContext, userUpgradePlan);
                    detail = PaypalSubscriptionModel.detail(apiContext, upgradeLog.getSrcSubscriptionId());
                    paypalUpgradeLogService.lambdaUpdate()
                            .eq(PaypalUpgradeLog::getId, upgradeLog.getId())
                            .set(PaypalUpgradeLog::getSrcSubStatus, detail.getStatus())
                            .update();
                } catch (Exception e) {
                    log.error("paypal:cancel:{}", srcSubscriptionId, e);
                    //发送告警信息
                    sendAlarmMessage("PayPal cancel exception, id: " + srcSubscriptionId, userId, e);
                    return;
                }
            }
            log.info("src subscriptionId: {} srcSubStatus2: {}", upgradeLog.getSrcSubscriptionId(), detail.getStatus());

            PayPalLogicSubscription payPalLogicSubscription = this.lambdaQuery()
                    .eq(PayPalLogicSubscription::getSubscriptionId, srcSubscriptionId)
                    .eq(PayPalLogicSubscription::getInvalid, false)
                    .one();
            if (!Boolean.TRUE.equals(upgradeLog.getVipRevert())) {
                try {
                    this.invalidById(payPalLogicSubscription.getId());
                    payLumenRecordService.invalidatedSubscriptionForPaypal(srcSubscriptionId, payPalLogicSubscription.getId());
                    paypalUpgradeLogService.lambdaUpdate()
                            .eq(PaypalUpgradeLog::getId, upgradeLog.getId())
                            .set(PaypalUpgradeLog::getVipRevert, true)
                            .update();
                } catch (Exception e) {
                    log.error("paypal:cancel:revert fail {}", srcSubscriptionId, e);
                    //发送告警信息
                    sendAlarmMessage("PayPal cancel revert exception, id: " + srcSubscriptionId, userId, e);
                    return;
                }
            }
            log.info("src subscriptionId: {} srcSubStatus3: {}", upgradeLog.getSrcSubscriptionId(), upgradeLog.getHasRefund());

            try {
                // 计算退款金额
                PayPalRefundRecord payPalRefundRecord = payPalRefundRecordService.calculateAndDoRefundAmount(payPalLogicSubscription, upgradeLog.getNewSubActiveTime());
                PaypalSubscriptionModel detail2 = PaypalSubscriptionModel.detail(apiContext, upgradeLog.getNewSubscriptionId());
                if (payPalRefundRecord == null) {
                    paypalUpgradeLogService.lambdaUpdate()
                            .eq(PaypalUpgradeLog::getId, upgradeLog.getId())
                            .set(PaypalUpgradeLog::getRefundId, -1)
                            .set(PaypalUpgradeLog::getNewSubStatus, detail2.getStatus())
                            .set(PaypalUpgradeLog::getHasRefund, true)
                            .set(PaypalUpgradeLog::getRefundAmount, 0)
                            .update();
                    return;
                }
                paypalUpgradeLogService.lambdaUpdate()
                        .eq(PaypalUpgradeLog::getId, upgradeLog.getId())
                        .set(PaypalUpgradeLog::getRefundId, payPalRefundRecord.getRefundId())
                        .set(PaypalUpgradeLog::getNewSubStatus, detail2.getStatus())
                        .set(PaypalUpgradeLog::getHasRefund, true)
                        .set(PaypalUpgradeLog::getRefundAmount, payPalRefundRecord.getTotal())
                        .update();
            } catch (Exception e) {
                log.error("paypal:cancel:calculateAndDoRefundAmount fail {}", srcSubscriptionId, e);
                //发送告警信息
                sendAlarmMessage("PayPal cancel calculateAndDoRefundAmount exception, id: " + srcSubscriptionId, userId, e);
                return;
            }
        } catch (PayPalRESTException e) {
            log.error("Failed to cancel", e);
            //发送告警信息
            sendAlarmMessage("PayPal cancel exception, id: " + srcSubscriptionId, userId, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public PaypalSubscriptionModel queryPaypalSubDetail(String subscriptionId) {
        try {
            PaypalSubscriptionModel subscriptionModel = PaypalSubscriptionModel.detail(apiContext, subscriptionId);
            return subscriptionModel;
        } catch (PayPalRESTException e) {
            log.error("Failed to get subscription detail", e);
            throw new RuntimeException(e);
        }
    }

    private PayPalLogicSubscription copyToNew(PayPalLogicSubscription src) {
        PayPalLogicSubscription newSub = new PayPalLogicSubscription();
        BeanUtils.copyProperties(src, newSub, "id", "createTime", "updateTime", "invalid", "createBy", "updateBy");
        return newSub;
    }

    private void invalidById(Long id) {
        this.lambdaUpdate()
                .eq(PayPalLogicSubscription::getId, id)
                .set(PayPalLogicSubscription::getInvalid, true)
                .set(PayPalLogicSubscription::getUpdateTime, LocalDateTime.now())
                .update();
    }

    protected static SubscriptionCurrent buildSubscriptionCurrent(PayPalLogicSubscription one, PayPalProduct product, PaypalSubscriptionModel model) {
        SubscriptionCurrent subscriptionCurrent = new SubscriptionCurrent();
        subscriptionCurrent.setUserId(one.getUserId());
        subscriptionCurrent.setLoginName(one.getLoginName());
        subscriptionCurrent.setSubscriptionId(one.getSubscriptionId());
        subscriptionCurrent.setVipPlatform(VipPlatform.PAYPAL.getPlatformName());
        subscriptionCurrent.setPlanLevel(product.getPlanLevel());
        log.info("buildSubscriptionCurrent {} start: {}", one.getSubscriptionId(), one.getSubStartSec());
        subscriptionCurrent.setCurrentPeriodStart(one.getSubStartSec());
        subscriptionCurrent.setCurrentPeriodEnd(one.getNextBillingSec() != null ? one.getNextBillingSec() + 24 * 60 * 60 : null);
        subscriptionCurrent.setVipBeginTime(subscriptionCurrent.getCurrentPeriodStart());
        subscriptionCurrent.setVipEndTime(subscriptionCurrent.getCurrentPeriodEnd());
        subscriptionCurrent.setPriceInterval(product.getPriceInterval());
        subscriptionCurrent.setAutoRenewStatus(1);
        subscriptionCurrent.setCreateBy("paypal");
        subscriptionCurrent.setCreateTime(LocalDateTime.now());
        subscriptionCurrent.setInvalid(false);
        subscriptionCurrent.setMark(product.getMark());
        subscriptionCurrent.setCreateTime(LocalDateTime.now());
        if (model.getBillingInfo() != null) {
            BillingInfo.LastPayment lastPayment = model.getBillingInfo().getLastPayment();
            if (lastPayment != null && lastPayment.getAmount() != null) {
                subscriptionCurrent.setRenewPrice(lastPayment.getAmount().getValue());
            } else {
                subscriptionCurrent.setRenewPrice(product.getPrice());
            }
        }
        if (subscriptionCurrent.getRenewPrice() == null) {
            PayPalPlanModel plan = model.getPlan();
            if (plan != null && plan.getBillingCycles() != null && !plan.getBillingCycles().isEmpty()) {
                BillingCycle billingCycle = plan.getBillingCycles().get(0);
                if (billingCycle != null && billingCycle.getPricingScheme() != null && billingCycle.getPricingScheme().getFixedPrice() != null) {
                    subscriptionCurrent.setRenewPrice(billingCycle.getPricingScheme().getFixedPrice().getValue());
                }
            }
        }
        if (subscriptionCurrent.getRenewPrice() == null) {
            subscriptionCurrent.setRenewPrice(product.getPrice());
        }
        log.info("buildSubscriptionCurrent: {}", subscriptionCurrent);
        return subscriptionCurrent;
    }

    private void sendAlarmMessage(String detail, Long userId, Exception e) {
        dingTalkAlert.sendAlarmMessage(LogicUtil.buildAlarmMessage(
                AlarmEnum.AlarmTypeEnum.EXCEPTION.getDescription(),
                AlarmEnum.AlarmSourceEnum.PAY_PAL.getDescription(),
                detail,
                userId + "",
                e));
    }
}
