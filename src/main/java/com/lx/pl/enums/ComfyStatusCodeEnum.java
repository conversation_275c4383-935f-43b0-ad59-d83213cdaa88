package com.lx.pl.enums;

public enum ComfyStatusCodeEnum {
    SUCCESS(2000, "success"),
    NO_FACE_DETECTED(3001, "No face detected!"),
    EXECUTION_ERROR(3003, "Execution error"),
    EXECUTION_INTERRUPTED(3004, "Execution interrupted"),
    NOT_FUND_HISTORY(3005, "Not find img from comfy history interface"),
    DEAL_RESULT_ERROR(3006, "Error handle Img or can't find img"),
    OUT_OF_MEMORY_GPU(3007, "Out of memory GPU"),
    PORNOGRAPHIC_CONTENT_DETECTED(3008, "Pornographic content detected"),
    INFRINGEMENT_RISK_DETECTED(3009, "Infringement risk detected"),
    UNKNOWN(9999, "unknown"),
    ;
    private Integer code;

    private String desc;

    //如果枚举值中还有数据则必须要创建一个构造函数
    ComfyStatusCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    // 根据错误码获取错误信息
    public static String getErrorMessage(int code) {
        for (ComfyStatusCodeEnum errorCode : ComfyStatusCodeEnum.values()) {
            if (errorCode.getCode() == code) {
                return errorCode.getDesc();
            }
        }
        return "Unknown error code: " + code;
    }
}
