package com.lx.pl.enums;

public enum NoticeUserType {
    vip("vip", "订阅用户"),
    not_vip("not_vip", "免费用户"),
    all("all","全部用户");

    private String value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    NoticeUserType(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static String getLabelByValue(String value) {
        for (OriginCreate originCreate : OriginCreate.values()) {
            if (originCreate.getValue().equals(value)) {
                return originCreate.getLabel();
            }
        }
        return "";
    }
}
