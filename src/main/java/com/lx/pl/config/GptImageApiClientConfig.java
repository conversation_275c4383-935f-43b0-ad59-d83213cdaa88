package com.lx.pl.config;

import com.lx.pl.client.GptImageApiClient;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.concurrent.TimeUnit;

/**
 * GPT Image API客户端配置
 *
 * <AUTHOR>
 */
@Configuration
public class GptImageApiClientConfig {

    @Autowired
    private GptImageConfig gptImageConfig;

    @Bean
    public GptImageApiClient gptImageApiClient() {
        // 创建日志拦截器
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
        if (gptImageConfig.getDebugEnabled()) {
            loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        } else {
            loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BASIC);
        }

        // 创建OkHttp客户端
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(gptImageConfig.getConnectTimeoutSeconds(), TimeUnit.SECONDS)
                .readTimeout(gptImageConfig.getReadTimeoutSeconds(), TimeUnit.SECONDS)
                .writeTimeout(gptImageConfig.getWriteTimeoutSeconds(), TimeUnit.SECONDS)
                .addInterceptor(loggingInterceptor)
                .build();

        // 创建Retrofit实例
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(gptImageConfig.getBaseUrl())
                .client(okHttpClient)
                .addConverterFactory(JacksonConverterFactory.create())
                .build();

        return retrofit.create(GptImageApiClient.class);
    }
}
