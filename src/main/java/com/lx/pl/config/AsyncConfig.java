package com.lx.pl.config;

import com.lx.pl.Handler.TranslateTaskRejectedExecutionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class AsyncConfig {

    @Resource
    private TranslateTaskRejectedExecutionHandler translateTaskRejectedExecutionHandler;

    @Bean(name = "gpuTaskExecutor")
    public ThreadPoolTaskExecutor gpuTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(30);       // 核心线程数
        executor.setMaxPoolSize(50);       // 最大线程数
        executor.setQueueCapacity(1000);    // 队列容量
        executor.setThreadNamePrefix("GpuAsyncTask-");
        executor.setRejectedExecutionHandler(translateTaskRejectedExecutionHandler); // 设置自定义拒绝策略
        executor.initialize();
        return executor;
    }

    @Bean(name = "scoreFlushExecutor")
    public ThreadPoolTaskExecutor scoreboardExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3);       // 核心线程数
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors());       // 最大线程数
        executor.setQueueCapacity(5000);    // 队列容量
        executor.setThreadNamePrefix("ScoreFlushTask-");
        executor.setRejectedExecutionHandler(translateTaskRejectedExecutionHandler); // 设置自定义拒绝策略
        executor.initialize();
        return executor;
    }

    @Bean(name = "taskExecutor")
    @Primary
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(30);       // 核心线程数
        executor.setMaxPoolSize(50);       // 最大线程数
        executor.setQueueCapacity(1000);    // 队列容量
        executor.setThreadNamePrefix("TaskExecutor-");
        executor.setRejectedExecutionHandler(translateTaskRejectedExecutionHandler); // 设置自定义拒绝策略
        executor.initialize();
        return executor;
    }

    @Bean(name = "dingTalkAlarmExecutor")
    public ThreadPoolTaskExecutor dingTalkAlarmExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);       // 核心线程数
        executor.setMaxPoolSize(20);       // 最大线程数
        executor.setQueueCapacity(1000);    // 队列容量
        executor.setThreadNamePrefix("dingTalkAlarm-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy()); // 丢弃队列里最老的任务
        executor.initialize();
        return executor;
    }

    @Bean(name = "userActionRecordExecutor")
    public ThreadPoolTaskExecutor userActionRecordExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);       // 核心线程数
        executor.setMaxPoolSize(10);       // 最大线程数
        executor.setQueueCapacity(1000);    // 队列容量
        executor.setThreadNamePrefix("userActionRecord-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy()); // 丢弃队列里最老的任务
        executor.initialize();
        return executor;
    }
}
