package com.lx.pl.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * GPT Image API配置
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "gpt-image")
public class GptImageConfig {

    /**
     * API基础URL
     */
    private String baseUrl = "https://api.openai.com";

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * 连接超时时间（秒）
     */
    private Integer connectTimeoutSeconds = 30;

    /**
     * 读取超时时间（秒）
     */
    private Integer readTimeoutSeconds = 120;

    /**
     * 写入超时时间（秒）
     */
    private Integer writeTimeoutSeconds = 120;

    /**
     * 最大并发任务数
     */
    private Integer maxConcurrentJobs = 10;

    /**
     * 任务轮询间隔（秒）
     */
    private Integer pollingIntervalSeconds = 2;

    /**
     * 最大轮询次数
     */
    private Integer maxPollingAttempts = 30;

    /**
     * 首次轮询延迟（秒）
     */
    private Integer firstDelaySeconds = 3;

    /**
     * 任务超时时间（秒）
     */
    private Integer taskTimeoutSeconds = 300;

    /**
     * 是否启用调试日志
     */
    private Boolean debugEnabled = false;

    /**
     * 默认图像质量
     */
    private String defaultQuality = "standard";

    /**
     * 默认图像风格
     */
    private String defaultStyle = "vivid";

    /**
     * 默认响应格式
     */
    private String defaultResponseFormat = "url";

    /**
     * 模拟异步处理延迟（毫秒）
     */
    private Integer asyncProcessDelayMs = 1000;
}
