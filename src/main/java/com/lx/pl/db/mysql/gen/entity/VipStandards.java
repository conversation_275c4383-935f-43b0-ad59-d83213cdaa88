package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@TableName("gpt_vip_standards")
@Data
public class VipStandards {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "会员类型： basic 非会员 standard 普通会员 pro 高级会员")
    private String vipType;

    @Schema(description = "lumen折扣 10表示sale10%")
    private Integer lumenDiscount;

    @Schema(description = "每日免费点数")
    private Integer dailyLumens;

    @Schema(description = "会员点数")
    private Integer monthlyLumens;

    @Schema(description = "历史页图片保留时长： 免费用户: 30 天  会员: 120 天 超级会员: never expire")
    private Integer creationHistory;

    @Schema(description = "排队任务数: 免费用户:0 会员: 5 超级会员: 10")
    private Integer taskQueue;

    @Schema(description = "并发任务数: 免费用户:0 会员: 2 超级会员: 5")
    private Integer concurrentJobs;

    @Schema(description = "是否可以批量下载： 0 否 1 是")
    private Boolean batchDownload;

    @Schema(description = "批量生图数： 2 ： 非会员 4 ： 普通会员 4 ： 高级会员")
    private Integer imagesPerBatch;

    @Schema(description = "收藏数标准容量")
    private Integer collectNum;

    @Schema(description = "是否可以超分： 0 否 1 是")
    private Boolean upscale;

    @Schema(description = "是否可以局部重绘： 0 否 1 是")
    private Boolean inpaint;

    @Schema(description = "是否可以局部扩图： 0 否 1 是")
    private Boolean expand;

    @Schema(description = "是否可以线稿上色： 0 否 1 是")
    private Boolean colorize;

    @Schema(description = "是否可以去背景： 0 否 1 是")
    private Boolean removeBg;

    @Schema(description = "是否可以历史探索： 0 否 1 是")
    private Boolean historyExplore;

    @Schema(description = "是否可以翻译： 0 否 1 是")
    private Boolean translation;

    @Schema(description = "是否可以增强： 0 否 1 是")
    private Boolean enhance;

    @Schema(description = "是否可以编辑图片：0-否，1-是")
    private Boolean editImg;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
