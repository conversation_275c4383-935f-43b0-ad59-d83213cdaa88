package com.lx.pl.openai.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.enums.VipType;
import com.lx.pl.openai.dto.images.OpenAiImagesRequest;
import com.lx.pl.openai.dto.images.OpenAiImagesResponse;
import com.lx.pl.openai.service.OpenAiImagesService;
import com.lx.pl.service.GenService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * OpenAI Images API 控制器测试
 *
 * <AUTHOR>
 */
@WebMvcTest(OpenAiImagesController.class)
class OpenAiImagesControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private OpenAiImagesService openAiImagesService;

    @MockBean
    private GenService genService;

    private User testUser;
    private OpenAiImagesRequest testRequest;
    private OpenAiImagesResponse testResponse;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // 创建测试用户
        testUser = new User();
        testUser.setLoginName("testuser");
        testUser.setVipType(VipType.premium.getValue());

        // 创建测试请求
        testRequest = new OpenAiImagesRequest();
        testRequest.setPrompt("A cute baby sea otter");
        testRequest.setModel("gpt-image-1");
        testRequest.setSize("1024x1024");
        testRequest.setQuality("standard");
        testRequest.setStyle("vivid");
        testRequest.setResponseFormat("url");
        testRequest.setN(1);

        // 创建测试响应
        OpenAiImagesResponse.ImageData imageData = OpenAiImagesResponse.ImageData.builder()
                .url("https://example.com/generated-image.png")
                .revisedPrompt("A cute baby sea otter floating on its back")
                .build();

        testResponse = OpenAiImagesResponse.builder()
                .created(System.currentTimeMillis() / 1000)
                .data(List.of(imageData))
                .build();
    }

    @Test
    void testHealthCheck() throws Exception {
        mockMvc.perform(get("/v1/images/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("ok"))
                .andExpect(jsonPath("$.service").value("openai-images-api"));
    }

    @Test
    void testGenerateImages_Success() throws Exception {
        // Mock dependencies
        when(genService.getPlatform(any())).thenReturn("web");
        when(openAiImagesService.generateImages(any(OpenAiImagesRequest.class), any(User.class), anyString()))
                .thenReturn(testResponse);

        mockMvc.perform(post("/v1/images/generations")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testRequest))
                        .requestAttr("currentUser", testUser))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.created").exists())
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].url").value("https://example.com/generated-image.png"))
                .andExpect(jsonPath("$.data[0].revised_prompt").value("A cute baby sea otter floating on its back"));
    }

    @Test
    void testGenerateImages_InvalidPrompt() throws Exception {
        OpenAiImagesRequest invalidRequest = new OpenAiImagesRequest();
        invalidRequest.setPrompt(""); // 空提示词
        invalidRequest.setModel("gpt-image-1");

        mockMvc.perform(post("/v1/images/generations")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest))
                        .requestAttr("currentUser", testUser))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testGenerateImages_InvalidModel() throws Exception {
        OpenAiImagesRequest invalidRequest = new OpenAiImagesRequest();
        invalidRequest.setPrompt("A test image");
        invalidRequest.setModel("gpt-image-1");
        invalidRequest.setN(2); // gpt-image-1 不支持生成多张图片

        when(genService.getPlatform(any())).thenReturn("web");

        mockMvc.perform(post("/v1/images/generations")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest))
                        .requestAttr("currentUser", testUser))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error.type").value("invalid_request"));
    }

    @Test
    void testGenerateImagesCompatible_Success() throws Exception {
        when(genService.getPlatform(any())).thenReturn("web");
        when(openAiImagesService.generateImages(any(OpenAiImagesRequest.class), any(User.class), anyString()))
                .thenReturn(testResponse);

        mockMvc.perform(post("/v1/images/generations/compatible")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testRequest))
                        .requestAttr("currentUser", testUser))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.created").exists())
                .andExpect(jsonPath("$.data.data").isArray())
                .andExpect(jsonPath("$.data.model").value("gpt-image-1"));
    }

    @Test
    void testRequestValidation() throws Exception {
        // 测试各种无效参数
        OpenAiImagesRequest invalidRequest = new OpenAiImagesRequest();
        invalidRequest.setPrompt("A" + "x".repeat(1001)); // 超过1000字符限制
        invalidRequest.setModel("gpt-image-1");

        when(genService.getPlatform(any())).thenReturn("web");

        mockMvc.perform(post("/v1/images/generations")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest))
                        .requestAttr("currentUser", testUser))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error.type").value("invalid_request"));
    }

    @Test
    void testUnsupportedModel() throws Exception {
        OpenAiImagesRequest invalidRequest = new OpenAiImagesRequest();
        invalidRequest.setPrompt("A test image");
        invalidRequest.setModel("dall-e-3"); // 不支持的模型
        invalidRequest.setSize("1024x1024");

        when(genService.getPlatform(any())).thenReturn("web");

        mockMvc.perform(post("/v1/images/generations")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest))
                        .requestAttr("currentUser", testUser))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error.type").value("invalid_request"));
    }
}
