# GPT-Image-1 模型更改说明

## 更改概述

已将 OpenAI Images API 接口从支持 DALL-E-2 和 DALL-E-3 模型更改为仅支持 `gpt-image-1` 模型。

## 具体更改

### 1. 请求参数更改

**文件**: `src/main/java/com/lx/pl/openai/dto/images/OpenAiImagesRequest.java`

- **模型字段**：
  - 默认值从 `"dall-e-3"` 改为 `"gpt-image-1"`
  - 允许值从 `{"dall-e-2", "dall-e-3"}` 改为 `{"gpt-image-1"}`

- **参数验证逻辑**：
  - 移除了 DALL-E-2 和 DALL-E-3 的特定限制
  - 简化为仅验证 `gpt-image-1` 模型
  - 保持 `gpt-image-1` 只支持生成1张图片的限制

- **参数描述**：
  - 移除了 "仅dall-e-3支持" 等特定模型限制的描述
  - 统一为支持所有参数（quality, style 等）

### 2. 服务层更改

**文件**: `src/main/java/com/lx/pl/openai/service/OpenAiImagesService.java`

- **模型映射**：
  - 简化 `getFluxModelId()` 方法
  - `gpt-image-1` 映射到 `flux-dev`（高质量模型）
  - 对不支持的模型抛出异常

- **参数处理**：
  - 将 DALL-E-3 特定参数处理改为 gpt-image-1

### 3. 控制器更改

**文件**: `src/main/java/com/lx/pl/openai/controller/OpenAiImagesController.java`

- **API 描述**：
  - 从 "兼容OpenAI DALL-E API" 改为 "兼容OpenAI Images API"

### 4. 文档更改

**文件**: `docs/OpenAI-Images-API.md`

- **标题和描述**：
  - 更新为支持 gpt-image-1 模型
  - 移除 DALL-E 相关描述

- **参数表格**：
  - 模型默认值改为 `"gpt-image-1"`
  - 模型选项改为仅 `gpt-image-1`
  - 移除模型特定的参数限制说明

- **尺寸支持表**：
  - 合并为单一模型 `gpt-image-1`
  - 支持所有尺寸：`256x256, 512x512, 1024x1024, 1792x1024, 1024x1792`

- **示例代码**：
  - 所有示例中的模型名称改为 `gpt-image-1`

- **模型映射**：
  - 简化为单一映射：`gpt-image-1` → `flux-dev`

### 5. 测试更改

**文件**: `src/test/java/com/lx/pl/openai/controller/OpenAiImagesControllerTest.java`

- **测试数据**：
  - 所有测试请求的模型改为 `gpt-image-1`
  - 更新测试断言中的模型验证

- **测试用例**：
  - 将 DALL-E-2 尺寸限制测试改为不支持模型测试
  - 更新错误消息验证

### 6. 错误代码

**文件**: `src/main/java/com/lx/pl/enums/LogicErrorCode.java`

- 添加了 `FLUX_GENERATION_FAILED("4029")` 错误码

## API 使用变化

### 之前（支持多模型）
```json
{
  "prompt": "A cute baby sea otter",
  "model": "dall-e-3",  // 或 "dall-e-2"
  "size": "1024x1024"
}
```

### 现在（仅支持 gpt-image-1）
```json
{
  "prompt": "A cute baby sea otter",
  "model": "gpt-image-1",  // 仅支持此模型
  "size": "1024x1024"
}
```

## 功能保持不变

以下功能保持完全不变：

1. **接口端点**：
   - `POST /v1/images/generations`
   - `POST /v1/images/generations/sync`
   - `POST /v1/images/generations/compatible`

2. **参数支持**：
   - 所有参数（prompt, quality, style, size, response_format 等）
   - 参数验证逻辑
   - 错误处理机制

3. **响应格式**：
   - 完全兼容 OpenAI API 响应格式
   - 错误响应格式

4. **集成方式**：
   - 与 FluxService 的集成
   - 权限验证和点数系统
   - 并发控制

## 兼容性说明

- **向后兼容**：现有使用 `gpt-image-1` 的客户端无需更改
- **不兼容**：使用 `dall-e-2` 或 `dall-e-3` 的客户端需要更新模型名称
- **功能等价**：`gpt-image-1` 提供与之前 `dall-e-3` 相同的功能和质量

## 建议

1. **客户端更新**：通知使用方将模型名称更改为 `gpt-image-1`
2. **文档更新**：确保所有相关文档都反映新的模型支持
3. **监控**：监控是否有客户端仍在使用旧模型名称
4. **渐进迁移**：可考虑添加模型别名支持以便平滑迁移
