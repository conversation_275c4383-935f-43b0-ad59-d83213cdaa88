# GPT Image 配置示例
# 在 application.yml 中添加以下配置

# GPT Image API 配置
gpt-image:
  # API基础URL
  base-url: https://api.openai.com
  # API密钥
  api-key: ${GPT_IMAGE_API_KEY:your-api-key-here}
  # 连接超时时间（秒）
  connect-timeout-seconds: 30
  # 读取超时时间（秒）
  read-timeout-seconds: 120
  # 写入超时时间（秒）
  write-timeout-seconds: 120
  # 最大并发任务数
  max-concurrent-jobs: 10
  # 任务轮询间隔（秒）
  polling-interval-seconds: 2
  # 最大轮询次数
  max-polling-attempts: 30
  # 首次轮询延迟（秒）
  first-delay-seconds: 3
  # 任务超时时间（秒）
  task-timeout-seconds: 300
  # 是否启用调试日志
  debug-enabled: false
  # 默认图像质量
  default-quality: standard
  # 默认图像风格
  default-style: vivid
  # 默认响应格式
  default-response-format: url
  # 模拟异步处理延迟（毫秒）
  async-process-delay-ms: 1000

# GPT Image 模型ID配置
gptImage1:
  modelId: gpt-image-1

# 环境变量配置示例
# GPT_IMAGE_API_KEY=sk-your-openai-api-key-here
